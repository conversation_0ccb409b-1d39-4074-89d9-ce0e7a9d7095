import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

// 获取单篇文章详情
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: articleId } = await params;

  if (!articleId) {
    return NextResponse.json({ error: 'Article ID required' }, { status: 400 });
  }

  try {
    const article = await prisma.article.findUnique({
      where: { id: articleId },
      include: {
        wechatAccount: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }

    return NextResponse.json(article);
  } catch (error) {
    console.error('获取文章详情失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
