'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../components/AuthProvider';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalSubscriptions: number;
  totalAccounts: number;
  todayNewUsers: number;
  todayNewSubscriptions: number;
}

interface RecentActivity {
  id: string;
  type: 'user_register' | 'subscription_add' | 'subscription_cancel';
  description: string;
  timestamp: string;
  user?: string;
}

export default function AdminDashboard() {
  const { token } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalSubscriptions: 0,
    totalAccounts: 0,
    todayNewUsers: 0,
    todayNewSubscriptions: 0
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, activityRes] = await Promise.all([
        fetch('/api/admin/stats', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/admin/activity', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData);
      }

      if (activityRes.ok) {
        const activityData = await activityRes.json();
        setRecentActivity(activityData);
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: '总用户数',
      value: stats.totalUsers,
      change: `+${stats.todayNewUsers}`,
      changeLabel: '今日新增',
      color: 'blue',
      icon: '👥'
    },
    {
      title: '活跃用户',
      value: stats.activeUsers,
      change: `${Math.round((stats.activeUsers / stats.totalUsers) * 100)}%`,
      changeLabel: '活跃率',
      color: 'green',
      icon: '✅'
    },
    {
      title: '总订阅数',
      value: stats.totalSubscriptions,
      change: `+${stats.todayNewSubscriptions}`,
      changeLabel: '今日新增',
      color: 'purple',
      icon: '📱'
    },
    {
      title: '公众号数量',
      value: stats.totalAccounts,
      change: '',
      changeLabel: '已收录',
      color: 'orange',
      icon: '📢'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_register': return '👤';
      case 'subscription_add': return '➕';
      case 'subscription_cancel': return '➖';
      default: return '📝';
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'user_register': return 'text-blue-600';
      case 'subscription_add': return 'text-green-600';
      case 'subscription_cancel': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <p className="text-gray-600">系统概览和最新动态</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className="text-2xl font-bold text-gray-900">{card.value.toLocaleString()}</p>
                {card.change && (
                  <p className="text-sm text-gray-500 mt-1">
                    <span className="text-green-600">{card.change}</span> {card.changeLabel}
                  </p>
                )}
              </div>
              <div className="text-3xl">{card.icon}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 最近活动 */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">最近活动</h2>
        </div>
        <div className="p-6">
          {recentActivity.length === 0 ? (
            <p className="text-gray-500 text-center py-8">暂无活动记录</p>
          ) : (
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <span className="text-xl">{getActivityIcon(activity.type)}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium ${getActivityColor(activity.type)}`}>
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(activity.timestamp).toLocaleString()}
                      {activity.user && ` • ${activity.user}`}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
