import { NextRequest, NextResponse } from 'next/server';
import { validateInviteCode } from '../../../lib/auth';

export async function POST(req: NextRequest) {
  try {
    const { inviteCode } = await req.json();

    if (!inviteCode) {
      return NextResponse.json(
        { error: '邀请码不能为空' },
        { status: 400 }
      );
    }

    const validation = await validateInviteCode(inviteCode);

    if (!validation.valid) {
      return NextResponse.json(
        { error: '邀请码无效或已过期' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      inviter: {
        name: validation.inviter?.name || '匿名用户',
        email: validation.inviter?.email
      },
      message: '邀请码有效'
    });

  } catch (error) {
    console.error('验证邀请码失败:', error);
    return NextResponse.json(
      { error: '验证失败，请稍后重试' },
      { status: 500 }
    );
  }
}
