import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const mockArticles = [
  {
    title: '科技创新推动经济高质量发展',
    summary: '近年来，我国科技创新能力不断提升，为经济高质量发展提供了强有力的支撑。科技创新是推动经济社会发展的第一动力，在新时代背景下发挥着重要作用。',
    url: 'https://mp.weixin.qq.com/s/tech-innovation-development',
    publishDate: new Date('2024-01-15'),
    readCount: 15420,
    likeCount: 892,
    coverImage: 'https://via.placeholder.com/600x300/4F46E5/FFFFFF?text=科技创新',
    author: '科技日报',
    tags: ['科技', '创新', '经济发展']
  },
  {
    title: '绿色发展理念引领生态文明建设',
    summary: '坚持绿色发展理念，推进生态文明建设，是实现可持续发展的必由之路。绿色发展是以效率、和谐、持续为目标的经济增长和社会发展方式。',
    url: 'https://mp.weixin.qq.com/s/green-development-ecology',
    publishDate: new Date('2024-01-12'),
    readCount: 12350,
    likeCount: 567,
    coverImage: 'https://via.placeholder.com/600x300/059669/FFFFFF?text=绿色发展',
    author: '环保专家',
    tags: ['绿色发展', '生态文明', '可持续发展']
  },
  {
    title: '数字经济助力产业转型升级',
    summary: '数字经济作为新的经济形态，正在深刻改变传统产业的发展模式。我国数字经济规模持续扩大，已成为推动经济增长的重要引擎。',
    url: 'https://mp.weixin.qq.com/s/digital-economy-transformation',
    publishDate: new Date('2024-01-10'),
    readCount: 18760,
    likeCount: 1024,
    coverImage: 'https://via.placeholder.com/600x300/7C3AED/FFFFFF?text=数字经济',
    author: '经济分析师',
    tags: ['数字经济', '产业转型', '技术创新']
  }
];

async function main() {
  console.log('开始初始化数据...');

  // 创建模拟用户
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'demo-user-id',
      email: '<EMAIL>',
      name: '演示用户',
      password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtrKG5wy', // password123
      inviteCode: 'DEMO0001'
    }
  });

  // 创建模拟公众号
  const accounts = [
    {
      id: 'rmrb_001',
      name: '人民日报',
      avatar: 'https://via.placeholder.com/100x100/4F46E5/FFFFFF?text=人民日报',
      openid: 'rmrb_001',
      description: '人民日报官方微信公众号，提供权威新闻资讯'
    },
    {
      id: 'kjrb_001',
      name: '科技日报',
      avatar: 'https://via.placeholder.com/100x100/7C3AED/FFFFFF?text=科技',
      openid: 'kjrb_001',
      description: '科技日报官方微信，科技前沿资讯'
    }
  ];

  for (const accountData of accounts) {
    const account = await prisma.wechatAccount.upsert({
      where: { openid: accountData.openid },
      update: accountData,
      create: accountData
    });

    // 为每个公众号创建文章
    for (let i = 0; i < mockArticles.length; i++) {
      const articleData = mockArticles[i];
      await prisma.article.upsert({
        where: { id: `${account.id}_article_${i + 1}` },
        update: {},
        create: {
          id: `${account.id}_article_${i + 1}`,
          title: `${account.name} - ${articleData.title}`,
          summary: articleData.summary,
          url: `${articleData.url}?from=${account.id}`,
          publishDate: new Date(articleData.publishDate.getTime() - i * 24 * 60 * 60 * 1000),
          readCount: articleData.readCount + Math.floor(Math.random() * 1000),
          likeCount: articleData.likeCount + Math.floor(Math.random() * 100),
          coverImage: articleData.coverImage,
          author: articleData.author || account.name,
          tags: Array.isArray(articleData.tags) ? articleData.tags.join(',') : '',
          wechatAccountId: account.id
        }
      });
    }
  }

  console.log('数据初始化完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
