import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../lib/auth';
import { ArticleDownloader } from '../../../../lib/article-downloader';

const prisma = new PrismaClient();

/**
 * 下载完全离线的文章（包含图片）
 * POST /api/articles/download-offline
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);

    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const { articleId, includeImages = true, options = {} } = body;

    if (!articleId) {
      return NextResponse.json({ error: '文章ID不能为空' }, { status: 400 });
    }

    // 获取文章信息
    const article = await prisma.article.findUnique({
      where: { id: articleId },
      include: {
        wechatAccount: true
      }
    });

    if (!article) {
      return NextResponse.json({ error: '文章不存在' }, { status: 404 });
    }

    // 检查用户是否已订阅该公众号
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        wechatAccountId: article.wechatAccountId,
        isActive: true
      }
    });

    if (!subscription) {
      return NextResponse.json({ error: '您未订阅该公众号' }, { status: 403 });
    }

    // 检查是否已有下载记录
    const existingDownload = await prisma.articleDownload.findFirst({
      where: {
        articleId: articleId,
        userId: user.id,
        status: 'COMPLETED'
      }
    });

    // 创建下载记录
    const downloadRecord = await prisma.articleDownload.create({
      data: {
        articleId: articleId,
        userId: user.id,
        status: 'DOWNLOADING',
        proxyServerId: null // 将在下载过程中更新
      }
    });

    console.log(`🚀 开始离线下载文章: ${article.title} (包含图片: ${includeImages})`);

    // 下载文章并处理图片
    const downloader = new ArticleDownloader();

    try {
      const { html, size } = await downloader.downloadArticleHTML(article.url, article.title, options);
      
      // 创建离线版本的HTML
      let offlineHtml: string;
      
      if (includeImages) {
        // 下载图片并嵌入HTML
        offlineHtml = await downloader['createOfflineHtml'](html, article.title, true);
      } else {
        // 只替换图片URL为代理URL
        offlineHtml = await downloader['createOfflineHtml'](html, article.title, false);
      }

      // 更新下载记录
      await prisma.articleDownload.update({
        where: { id: downloadRecord.id },
        data: {
          status: 'COMPLETED',
          filePath: `offline_${articleId}_${Date.now()}.html`,
          fileSize: new Blob([offlineHtml]).size,
          downloadTime: new Date()
        }
      });

      console.log(`✅ 离线文章下载完成: ${article.title}`);

      // 返回离线HTML供前端下载
      return NextResponse.json({
        success: true,
        message: includeImages ? '离线文章下载成功（包含图片）' : '文章下载成功',
        downloadId: downloadRecord.id,
        article: {
          id: articleId,
          title: article.title,
          html: offlineHtml,
          size: new Blob([offlineHtml]).size,
          includeImages
        }
      });

    } catch (error) {
      console.error(`❌ 离线下载失败: ${article.title}`, error);

      // 更新下载记录为失败状态
      await prisma.articleDownload.update({
        where: { id: downloadRecord.id },
        data: {
          status: 'FAILED',
          errorMessage: error instanceof Error ? error.message : '下载失败'
        }
      });

      return NextResponse.json({
        error: error instanceof Error ? error.message : '下载失败'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('离线下载API错误:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
