#!/usr/bin/env node

/**
 * 测试 TypeScript 模块导入
 */

async function testImports() {
  console.log('🧪 测试 TypeScript 模块导入...');
  
  try {
    // 测试 startup 模块导入
    console.log('📦 导入 startup 模块...');
    const startupModule = await import('../lib/startup.ts');
    console.log('startup 模块内容:', Object.keys(startupModule));
    console.log('initializeApp 类型:', typeof startupModule.initializeApp);
    console.log('cleanupApp 类型:', typeof startupModule.cleanupApp);
    
    // 测试 scheduler 模块导入
    console.log('📦 导入 scheduler 模块...');
    const schedulerModule = await import('../lib/wechat-crawler-scheduler.ts');
    console.log('scheduler 模块内容:', Object.keys(schedulerModule));
    console.log('WechatCrawlerScheduler 类型:', typeof schedulerModule.WechatCrawlerScheduler);
    
    // 验证函数调用
    if (typeof startupModule.initializeApp === 'function') {
      console.log('✅ initializeApp 函数导入成功');
    } else {
      console.log('❌ initializeApp 导入失败');
    }
    
    if (typeof startupModule.cleanupApp === 'function') {
      console.log('✅ cleanupApp 函数导入成功');
    } else {
      console.log('❌ cleanupApp 导入失败');
    }
    
    if (typeof schedulerModule.WechatCrawlerScheduler === 'function') {
      console.log('✅ WechatCrawlerScheduler 类导入成功');
    } else {
      console.log('❌ WechatCrawlerScheduler 导入失败');
    }
    
    console.log('🎉 所有模块导入测试完成');
    
  } catch (error) {
    console.error('❌ 模块导入测试失败:', error);
    process.exit(1);
  }
}

testImports();
