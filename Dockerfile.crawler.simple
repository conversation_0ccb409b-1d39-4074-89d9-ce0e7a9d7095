# 定时任务爬虫服务 Dockerfile - 简化版本
FROM node:18-alpine

# 安装必要的系统依赖
RUN apk add --no-cache \
    curl

# 设置工作目录
WORKDIR /app

# 先复制 Docker 专用的 package.json
COPY docker/package.json ./package.json

# 安装生产依赖
RUN npm install --production --no-audit --no-fund

# 复制必要的源代码文件
COPY prisma/ ./prisma/
COPY docker/crawler-service-cjs.js ./crawler-service.js

# 生成 Prisma 客户端
RUN npx prisma generate

# 设置环境变量
ENV NODE_ENV=production
ENV DISABLE_CRAWLER_SCHEDULER=false

# 暴露端口（用于健康检查）
EXPOSE 3001

# 启动定时任务服务
CMD ["node", "crawler-service.js"]
