import { NextResponse } from 'next/server';
import { autoInitialize, forceReinitialize } from '../../../lib/auto-init';
import { WechatCrawlerScheduler } from '../../../lib/wechat-crawler-scheduler';

// 手动初始化端点
export async function POST() {
  try {
    console.log('📡 收到手动初始化请求...');
    await forceReinitialize();

    // 检查定时任务状态
    const scheduler = WechatCrawlerScheduler.getInstance();
    const status = scheduler.getStatus();

    return NextResponse.json({
      success: true,
      message: '应用初始化完成',
      schedulerStatus: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('应用初始化失败:', error);
    return NextResponse.json(
      {
        error: '初始化失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取初始化状态
export async function GET() {
  try {
    // 确保自动初始化已执行
    await autoInitialize();

    // 检查定时任务状态
    const scheduler = WechatCrawlerScheduler.getInstance();
    const status = scheduler.getStatus();

    return NextResponse.json({
      success: true,
      message: '应用运行中',
      schedulerStatus: status,
      autoInitialized: true,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    console.error('获取状态失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取状态失败',
      schedulerStatus: { isRunning: false, consecutiveFailures: 0, maxConsecutiveFailures: 5, isInAllowedTime: false },
      autoInitialized: false,
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}
