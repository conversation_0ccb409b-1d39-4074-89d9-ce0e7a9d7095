// Cloudflare Worker for API caching and acceleration
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // 缓存静态资源
  if (url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$/)) {
    const cache = caches.default
    const cacheKey = new Request(url.toString(), request)
    
    let response = await cache.match(cacheKey)
    
    if (!response) {
      response = await fetch(request)
      
      if (response.status === 200) {
        const headers = new Headers(response.headers)
        headers.set('Cache-Control', 'public, max-age=31536000') // 1年
        headers.set('Expires', new Date(Date.now() + 31536000000).toUTCString())
        
        response = new Response(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: headers
        })
        
        event.waitUntil(cache.put(cacheKey, response.clone()))
      }
    }
    
    return response
  }
  
  // 缓存API响应（短时间）
  if (url.pathname.startsWith('/api/')) {
    const cache = caches.default
    const cacheKey = new Request(url.toString(), {
      method: request.method,
      headers: request.headers
    })
    
    // 只缓存GET请求
    if (request.method === 'GET') {
      let response = await cache.match(cacheKey)
      
      if (!response) {
        response = await fetch(request)
        
        if (response.status === 200) {
          const headers = new Headers(response.headers)
          headers.set('Cache-Control', 'public, max-age=300') // 5分钟
          
          response = new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: headers
          })
          
          event.waitUntil(cache.put(cacheKey, response.clone()))
        }
      }
      
      return response
    }
  }
  
  // 其他请求直接转发
  return fetch(request)
}
