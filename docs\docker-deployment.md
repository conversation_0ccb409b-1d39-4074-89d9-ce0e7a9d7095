# FeedWe Docker 部署指南

## 概述

由于 Vercel 免费版本不支持定时任务，我们将定时任务功能分离为独立的 Docker 服务。这样的架构设计有以下优势：

- **Vercel 部署**: 负责 Web 界面和 API 服务
- **Docker 服务**: 负责定时任务和 Webhook 通知

## 架构说明

```
┌─────────────────┐    ┌─────────────────┐
│   Vercel 部署   │    │  Docker 服务    │
│                 │    │                 │
│ • Web 界面      │    │ • 定时爬取任务  │
│ • API 服务      │    │ • Webhook 通知  │
│ • 用户管理      │    │ • 健康检查      │
│ • 手动爬取      │    │                 │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                共享数据库
```

## 前置要求

- Docker 和 Docker Compose
- 已部署的 Vercel 应用
- 数据库访问权限

## 快速部署

### 1. 准备环境配置

```bash
# 复制环境变量模板
cp .env.crawler.example .env.crawler

# 编辑环境变量
nano .env.crawler
```

### 2. 配置环境变量

在 `.env.crawler` 文件中配置以下变量：

```env
# 数据库连接（与 Vercel 应用使用相同数据库）
DATABASE_URL="mysql://username:password@host:port/database_name"

# 微信公众号爬虫配置
WECHAT_ARTICLE_COOKIE_STRING="your_wechat_cookie_string_here"
WECHAT_ARTICLE_TOKEN="your_wechat_token_here"

# 企业微信Webhook配置（可选）
WEWORK_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_webhook_key"

# 定时任务配置
DISABLE_CRAWLER_SCHEDULER=false

# 时区配置
TZ=Asia/Shanghai
```

### 3. 一键部署

```bash
# 使用部署脚本
chmod +x docker/deploy.sh
./docker/deploy.sh
```

或者手动部署：

```bash
# 构建并启动服务
docker-compose -f docker-compose.crawler.yml up -d --build

# 查看日志
docker-compose -f docker-compose.crawler.yml logs -f
```

## 服务管理

### 查看服务状态

```bash
# 查看容器状态
docker-compose -f docker-compose.crawler.yml ps

# 查看服务日志
docker-compose -f docker-compose.crawler.yml logs -f feedwe-crawler

# 健康检查
curl http://localhost:3001/health
```

### 重启服务

```bash
# 重启服务
docker-compose -f docker-compose.crawler.yml restart

# 重新构建并重启
docker-compose -f docker-compose.crawler.yml up -d --build
```

### 停止服务

```bash
# 停止服务
docker-compose -f docker-compose.crawler.yml down

# 停止并删除数据卷
docker-compose -f docker-compose.crawler.yml down -v
```

## 监控和维护

### 健康检查端点

- **健康检查**: `http://localhost:3001/health`
- **状态查询**: `http://localhost:3001/status`

### 日志管理

```bash
# 查看实时日志
docker-compose -f docker-compose.crawler.yml logs -f

# 查看最近100行日志
docker-compose -f docker-compose.crawler.yml logs --tail=100

# 导出日志到文件
docker-compose -f docker-compose.crawler.yml logs > crawler.log
```

### 性能监控

```bash
# 查看容器资源使用情况
docker stats feedwe-crawler

# 进入容器内部
docker exec -it feedwe-crawler sh
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库连接
   docker exec -it feedwe-crawler npx prisma db push --preview-feature
   ```

2. **微信登录失效**
   - 检查 `WECHAT_ARTICLE_COOKIE_STRING` 和 `WECHAT_ARTICLE_TOKEN` 是否有效
   - 重新获取微信登录凭据

3. **Webhook 通知失败**
   - 检查 `WEWORK_WEBHOOK_URL` 配置
   - 验证 Webhook 地址是否可访问

### 调试模式

```bash
# 以调试模式启动
docker-compose -f docker-compose.crawler.yml up --build

# 查看详细日志
docker-compose -f docker-compose.crawler.yml logs -f
```

## 生产环境建议

### 1. 资源限制

在 `docker-compose.crawler.yml` 中添加资源限制：

```yaml
services:
  feedwe-crawler:
    # ... 其他配置
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

### 2. 日志轮转

配置日志轮转避免日志文件过大：

```yaml
services:
  feedwe-crawler:
    # ... 其他配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 3. 自动重启

确保服务异常退出时自动重启：

```yaml
services:
  feedwe-crawler:
    # ... 其他配置
    restart: unless-stopped
```

### 4. 备份策略

定期备份重要数据：

```bash
# 备份日志
cp -r logs/ backup/logs-$(date +%Y%m%d)/

# 备份配置
cp .env.crawler backup/env-$(date +%Y%m%d).backup
```

## 更新升级

### 1. 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
./docker/deploy.sh
```

### 2. 数据库迁移

```bash
# 运行数据库迁移
docker exec -it feedwe-crawler npx prisma migrate deploy
```

## 安全建议

1. **网络安全**: 确保只有必要的端口对外开放
2. **环境变量**: 妥善保管 `.env.crawler` 文件，不要提交到版本控制
3. **定期更新**: 定期更新 Docker 镜像和依赖包
4. **监控告警**: 设置服务异常告警机制

## 支持

如果遇到问题，请：

1. 查看服务日志
2. 检查环境变量配置
3. 验证数据库连接
4. 确认微信登录状态

更多技术支持，请联系开发团队。
