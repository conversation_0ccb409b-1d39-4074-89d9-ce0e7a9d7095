import { PrismaClient } from '@prisma/client';
import { ArticleDownloader } from '../lib/article-downloader';

const prisma = new PrismaClient();

async function testDownloadFeature() {
  console.log('🧪 开始测试文章下载功能...');

  try {
    // 1. 检查代理服务器配置
    console.log('\n1️⃣ 检查代理服务器配置...');
    const proxyServers = await prisma.proxyServer.findMany({
      where: { isActive: true },
      orderBy: { priority: 'asc' }
    });

    console.log(`✅ 找到 ${proxyServers.length} 个活跃的代理服务器:`);
    proxyServers.forEach((proxy, index) => {
      console.log(`   ${index + 1}. ${proxy.name} (优先级: ${proxy.priority})`);
      console.log(`      URL: ${proxy.url}`);
      console.log(`      配置: 重试${proxy.maxRetries}次, 超时${proxy.timeout}秒`);
    });

    // 2. 检查文章数据
    console.log('\n2️⃣ 检查文章数据...');
    const articleCount = await prisma.article.count();
    console.log(`✅ 数据库中共有 ${articleCount} 篇文章`);

    if (articleCount > 0) {
      const sampleArticle = await prisma.article.findFirst({
        include: {
          wechatAccount: {
            select: {
              name: true
            }
          }
        },
        orderBy: {
          publishDate: 'desc'
        }
      });

      if (sampleArticle) {
        console.log(`   示例文章: "${sampleArticle.title}"`);
        console.log(`   公众号: ${sampleArticle.wechatAccount.name}`);
        console.log(`   发布时间: ${sampleArticle.publishDate}`);
        console.log(`   URL: ${sampleArticle.url}`);
      }
    }

    // 3. 检查用户和订阅数据
    console.log('\n3️⃣ 检查用户和订阅数据...');
    const userCount = await prisma.user.count();
    const subscriptionCount = await prisma.subscription.count({
      where: { isActive: true }
    });

    console.log(`✅ 用户数量: ${userCount}`);
    console.log(`✅ 活跃订阅数量: ${subscriptionCount}`);

    // 4. 测试下载器初始化
    console.log('\n4️⃣ 测试下载器初始化...');
    const downloader = new ArticleDownloader();
    console.log('✅ ArticleDownloader 初始化成功');

    // 5. 检查下载记录表
    console.log('\n5️⃣ 检查下载记录表...');
    const downloadCount = await prisma.articleDownload.count();
    console.log(`✅ 下载记录数量: ${downloadCount}`);

    if (downloadCount > 0) {
      const downloadStats = await prisma.articleDownload.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      });

      console.log('   下载状态统计:');
      downloadStats.forEach(stat => {
        console.log(`   - ${stat.status}: ${stat._count.status} 条`);
      });
    }

    // 6. 测试系统配置
    console.log('\n6️⃣ 检查系统配置...');
    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: ['wechat_token', 'wechat_cookie', 'wechat_data_ticket']
        }
      }
    });

    console.log(`✅ 系统配置项数量: ${configs.length}`);
    configs.forEach(config => {
      const hasValue = config.value && config.value.length > 0;
      console.log(`   - ${config.name}: ${hasValue ? '已配置' : '未配置'}`);
    });

    console.log('\n🎉 文章下载功能测试完成！');
    console.log('\n📋 功能状态总结:');
    console.log(`   ✅ 代理服务器: ${proxyServers.length} 个`);
    console.log(`   ✅ 文章数据: ${articleCount} 篇`);
    console.log(`   ✅ 用户数据: ${userCount} 个`);
    console.log(`   ✅ 订阅数据: ${subscriptionCount} 个`);
    console.log(`   ✅ 下载记录: ${downloadCount} 条`);
    console.log(`   ✅ 系统配置: ${configs.length} 项`);

    if (proxyServers.length === 0) {
      console.log('\n⚠️  警告: 没有配置代理服务器，下载功能可能无法正常工作');
      console.log('   建议运行: npx tsx scripts/init-proxy-servers.ts');
    }

    if (articleCount === 0) {
      console.log('\n⚠️  警告: 没有文章数据，无法测试下载功能');
      console.log('   建议先添加一些公众号并爬取文章');
    }

    if (configs.length === 0) {
      console.log('\n⚠️  警告: 没有配置微信凭据，下载可能失败');
      console.log('   建议在系统配置中添加微信相关配置');
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testDownloadFeature()
    .then(() => {
      console.log('\n✨ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试失败:', error);
      process.exit(1);
    });
}

export { testDownloadFeature };
