<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="34b207fb-6c85-44b1-b50f-92b2195f2ccd" name="Changes" comment="feat(admin): 增加微信图片代理功能&#10;&#10;- 新增微信图片代理工具函数，用于处理微信图片URL&#10;- 在管理员界面的多个组件中集成该功能，包括订阅页面、文章列表页面等- 优化文章导出功能，支持通过POST方法导出指定文章ID&#10;- 调整公众号搜索逻辑，使用真实API替换模拟数据">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/FeedWe.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/api/crawler/crawler/route.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/article-downloader.ts" beforeDir="false" afterPath="$PROJECT_DIR$/lib/article-downloader.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/wechat-crawler-scheduler.ts" beforeDir="false" afterPath="$PROJECT_DIR$/lib/wechat-crawler-scheduler.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/wechat-crawler-service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/lib/wechat-crawler-service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/wechat-crawler.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-full-crawler.js" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/test-full-crawler.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-login-with-cookie-save.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-mock-login-simple.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-mock-login.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-qr-display.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-search-accounts.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-sub-articles-notification.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-system-config.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-vercel-cron.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-webhook-notifications.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-webhook.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/test-wechat-login.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Saul-Zhang&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;**************:Saul-Zhang/FeedWe.git&quot;,
    &quot;accountId&quot;: &quot;eeab7b8b-7475-4b08-8c20-d15ab0545165&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="30YLwT7wJ0oYZ9zzJTrFQCy2M2r" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "js.debugger.nextJs.config.created.client": "true",
    "js.debugger.nextJs.config.created.server": "true",
    "last_opened_file_path": "D:/myWork/FeedWe",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.Next.js: server-side.executor": "Debug",
    "ts.external.directory.path": "D:\\myWork\\FeedWe\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="npm.Next.js: server-side">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="34b207fb-6c85-44b1-b50f-92b2195f2ccd" name="Changes" comment="" />
      <created>1753799797707</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753799797707</updated>
      <workItem from="1753799800115" duration="18606000" />
      <workItem from="1753859215182" duration="474000" />
      <workItem from="1753859709031" duration="2818000" />
      <workItem from="1753881720942" duration="1455000" />
      <workItem from="1753926371453" duration="12257000" />
    </task>
    <task id="LOCAL-00001" summary="refactor(crawler): 优化 Cookie 保存逻辑并增加备用方案- 登录成功后增加 Cookie保存检查&#10;- 添加直接保存 Cookie 到数据库的备用方法&#10;- 优化关键参数显示，增加 Cookie 完整性提示&#10;-调整 API 调用 URL 获取方式，提高灵活性">
      <option name="closed" value="true" />
      <created>1753800556968</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753800556968</updated>
    </task>
    <task id="LOCAL-00002" summary="refactor(login): 重构登录流程并增强 Cookie 获取逻辑&#10;&#10;-移除单独的 getFinalLoginCookies 方法，直接在登录流程中处理 Cookie&#10;- 增加对关键 Cookie 字段的检查和分析&#10;- 调整用户信息获取步骤，主要用于验证 Cookie 有效性&#10;- 优化日志输出，提供更详细的 Cookie 获取信息">
      <option name="closed" value="true" />
      <created>1753801915584</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753801915584</updated>
    </task>
    <task id="LOCAL-00003" summary="refactor(crawler): 重构微信登录 Cookie 保存逻辑&#10;&#10;- 新增 onCookieSave 回调接口，用于在服务器环境中保存 Cookie到数据库&#10;- 修改 saveCookiesDirectly 方法，增加对浏览器环境的处理&#10;- 优化 Cookie 保存流程，减少不必要的 API 调用&#10;- 改进错误处理机制，确保 Cookie 保存的可靠性">
      <option name="closed" value="true" />
      <created>1753839252455</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753839252455</updated>
    </task>
    <task id="LOCAL-00004" summary="feat(login): 优化登录流程和轮询策略&#10;&#10;- 动态调整轮询频率，提高响应速度&#10;- 新增快速登录逻辑，减少不必要的步骤&#10;- 优化 Cookie 验证和用户信息获取- 重构部分代码结构，提高可维护性">
      <option name="closed" value="true" />
      <created>1753841775744</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753841775744</updated>
    </task>
    <task id="LOCAL-00005" summary="feat(wechat-crawler-login):增加最终登录步骤的调试信息和错误处理&#10;&#10;- 在最终登录步骤前添加调试信息，包括当前时间、session状态、cookie数量等&#10;- 在 performFinalLogin 中添加详细的错误捕获和日志记录，包括错误类型、消息、堆栈等&#10;- 实现请求超时机制，防止长时间运行的请求导致卡死&#10;- 在捕获到错误时尝试使用现有 Cookie 完成登录，增加容错性&#10;- 优化错误处理流程，确保错误信息能够正确传递给回调函数和事件监听器">
      <option name="closed" value="true" />
      <created>1753843138725</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753843138725</updated>
    </task>
    <task id="LOCAL-00006" summary="refactor(prisma): 优化数据库操作和错误处理&#10;&#10;- 添加环境检查，跳过浏览器环境下的数据库操作&#10;- 增加详细的日志输出，便于调试和监控&#10;- 优化 upsert 操作，更新时添加 updatedAt 字段- 改进错误处理，避免因数据库错误导致流程中断&#10;- 保存完成后断开数据库连接，释放资源">
      <option name="closed" value="true" />
      <created>1753849009092</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753849009092</updated>
    </task>
    <task id="LOCAL-00007" summary="refactor(wechat): 重构微信登录和爬虫相关功能&#10;&#10;- 移除 wechat_article_cookie_string 配置项，改用动态构建 Cookie 字符串&#10;- 更新微信爬虫和登录相关 API，使用新的 Cookie 构建逻辑- 修改环境变量配置，删除 WECHAT_ARTICLE_COOKIE_STRING 变量&#10;- 优化微信登录流程，增加会话状态检查和清理机制">
      <option name="closed" value="true" />
      <created>1753854173301</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753854173301</updated>
    </task>
    <task id="LOCAL-00008" summary="perf(crawler): 优化轮询逻辑并处理会话确认状态- 在 page.tsx 中增加了对当前会话状态的检查，避免在确认状态下继续轮询&#10;- 调整了轮询间隔为 2 秒，提高响应速度&#10;- 在 wechat-crawler-login.ts 中增加了对已确认会话的处理，防止继续检查状态&#10;- 在 article 更新逻辑中增加了对 URL 变化的检测和更新&#10;- 修改了 Prisma schema，将 article 表的 url 字段改为 TEXT 类型">
      <option name="closed" value="true" />
      <created>1753865334809</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753865334809</updated>
    </task>
    <task id="LOCAL-00009" summary="feat(article): 新增文章批量下载和导出功能&#10;&#10;-增加批量操作模式，可选择多篇文章进行批量下载和导出&#10;- 新增导出Excel功能，支持将选定文章导出为Excel文件&#10;- 优化文章列表界面，增加选择框和操作按钮&#10;- 更新定价页面，添加单篇文章下载和批量打包下载功能说明&#10;- 新增ArticleDownload和ProxyServer类型，用于文章下载相关功能">
      <option name="closed" value="true" />
      <created>1753926712954</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753926712954</updated>
    </task>
    <task id="LOCAL-00010" summary="feat(admin): 增加微信图片代理功能&#10;&#10;- 新增微信图片代理工具函数，用于处理微信图片URL&#10;- 在管理员界面的多个组件中集成该功能，包括订阅页面、文章列表页面等- 优化文章导出功能，支持通过POST方法导出指定文章ID&#10;- 调整公众号搜索逻辑，使用真实API替换模拟数据">
      <option name="closed" value="true" />
      <created>1753936950096</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753936950096</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/next.config.js" />
      </list>
    </option>
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="javascript:npm:react" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="javascript:npm:tailwindcss" />
    <option featureType="dependencySupport" implementationName="javascript:npm:prisma" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="javascript:npm:next" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="refactor(crawler): 优化 Cookie 保存逻辑并增加备用方案- 登录成功后增加 Cookie保存检查&#10;- 添加直接保存 Cookie 到数据库的备用方法&#10;- 优化关键参数显示，增加 Cookie 完整性提示&#10;-调整 API 调用 URL 获取方式，提高灵活性" />
    <MESSAGE value="refactor(login): 重构登录流程并增强 Cookie 获取逻辑&#10;&#10;-移除单独的 getFinalLoginCookies 方法，直接在登录流程中处理 Cookie&#10;- 增加对关键 Cookie 字段的检查和分析&#10;- 调整用户信息获取步骤，主要用于验证 Cookie 有效性&#10;- 优化日志输出，提供更详细的 Cookie 获取信息" />
    <MESSAGE value="refactor(crawler): 重构微信登录 Cookie 保存逻辑&#10;&#10;- 新增 onCookieSave 回调接口，用于在服务器环境中保存 Cookie到数据库&#10;- 修改 saveCookiesDirectly 方法，增加对浏览器环境的处理&#10;- 优化 Cookie 保存流程，减少不必要的 API 调用&#10;- 改进错误处理机制，确保 Cookie 保存的可靠性" />
    <MESSAGE value="feat(login): 优化登录流程和轮询策略&#10;&#10;- 动态调整轮询频率，提高响应速度&#10;- 新增快速登录逻辑，减少不必要的步骤&#10;- 优化 Cookie 验证和用户信息获取- 重构部分代码结构，提高可维护性" />
    <MESSAGE value="feat(wechat-crawler-login):增加最终登录步骤的调试信息和错误处理&#10;&#10;- 在最终登录步骤前添加调试信息，包括当前时间、session状态、cookie数量等&#10;- 在 performFinalLogin 中添加详细的错误捕获和日志记录，包括错误类型、消息、堆栈等&#10;- 实现请求超时机制，防止长时间运行的请求导致卡死&#10;- 在捕获到错误时尝试使用现有 Cookie 完成登录，增加容错性&#10;- 优化错误处理流程，确保错误信息能够正确传递给回调函数和事件监听器" />
    <MESSAGE value="refactor(prisma): 优化数据库操作和错误处理&#10;&#10;- 添加环境检查，跳过浏览器环境下的数据库操作&#10;- 增加详细的日志输出，便于调试和监控&#10;- 优化 upsert 操作，更新时添加 updatedAt 字段- 改进错误处理，避免因数据库错误导致流程中断&#10;- 保存完成后断开数据库连接，释放资源" />
    <MESSAGE value="refactor(wechat): 重构微信登录和爬虫相关功能&#10;&#10;- 移除 wechat_article_cookie_string 配置项，改用动态构建 Cookie 字符串&#10;- 更新微信爬虫和登录相关 API，使用新的 Cookie 构建逻辑- 修改环境变量配置，删除 WECHAT_ARTICLE_COOKIE_STRING 变量&#10;- 优化微信登录流程，增加会话状态检查和清理机制" />
    <MESSAGE value="perf(crawler): 优化轮询逻辑并处理会话确认状态- 在 page.tsx 中增加了对当前会话状态的检查，避免在确认状态下继续轮询&#10;- 调整了轮询间隔为 2 秒，提高响应速度&#10;- 在 wechat-crawler-login.ts 中增加了对已确认会话的处理，防止继续检查状态&#10;- 在 article 更新逻辑中增加了对 URL 变化的检测和更新&#10;- 修改了 Prisma schema，将 article 表的 url 字段改为 TEXT 类型" />
    <MESSAGE value="feat(article): 新增文章批量下载和导出功能&#10;&#10;-增加批量操作模式，可选择多篇文章进行批量下载和导出&#10;- 新增导出Excel功能，支持将选定文章导出为Excel文件&#10;- 优化文章列表界面，增加选择框和操作按钮&#10;- 更新定价页面，添加单篇文章下载和批量打包下载功能说明&#10;- 新增ArticleDownload和ProxyServer类型，用于文章下载相关功能" />
    <MESSAGE value="feat(admin): 增加微信图片代理功能&#10;&#10;- 新增微信图片代理工具函数，用于处理微信图片URL&#10;- 在管理员界面的多个组件中集成该功能，包括订阅页面、文章列表页面等- 优化文章导出功能，支持通过POST方法导出指定文章ID&#10;- 调整公众号搜索逻辑，使用真实API替换模拟数据" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(admin): 增加微信图片代理功能&#10;&#10;- 新增微信图片代理工具函数，用于处理微信图片URL&#10;- 在管理员界面的多个组件中集成该功能，包括订阅页面、文章列表页面等- 优化文章导出功能，支持通过POST方法导出指定文章ID&#10;- 调整公众号搜索逻辑，使用真实API替换模拟数据" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/lib/wechat-crawler-service.ts</url>
          <line>196</line>
          <properties lambdaOrdinal="-1" />
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>