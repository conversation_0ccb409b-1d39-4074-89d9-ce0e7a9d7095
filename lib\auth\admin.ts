// 管理员权限管理
export enum AdminRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  MODERATOR = 'MODERATOR'
}

export enum Permission {
  USER_MANAGEMENT = 'user_management',
  SUBSCRIPTION_MANAGEMENT = 'subscription_management',
  SYSTEM_SETTINGS = 'system_settings',
  ANALYTICS = 'analytics',
  CONTENT_MODERATION = 'content_moderation'
}

export const ROLE_PERMISSIONS: Record<AdminRole, Permission[]> = {
  [AdminRole.SUPER_ADMIN]: [
    Permission.USER_MANAGEMENT,
    Permission.SUBSCRIPTION_MANAGEMENT,
    Permission.SYSTEM_SETTINGS,
    Permission.ANALYTICS,
    Permission.CONTENT_MODERATION
  ],
  [AdminRole.ADMIN]: [
    Permission.USER_MANAGEMENT,
    Permission.SUBSCRIPTION_MANAGEMENT,
    Permission.ANALYTICS,
    Permission.CONTENT_MODERATION
  ],
  [AdminRole.MODERATOR]: [
    Permission.CONTENT_MODERATION,
    Permission.ANALYTICS
  ]
};

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: AdminRole;
  createdAt: string;
  lastLoginAt?: string;
  isActive: boolean;
}

export function hasPermission(role: AdminRole, permission: Permission): boolean {
  return ROLE_PERMISSIONS[role]?.includes(permission) || false;
}

export function isAdmin(user: any): user is AdminUser {
  return user && Object.values(AdminRole).includes(user.role);
}

// 管理员认证中间件
export function requireAdmin(requiredPermission?: Permission) {
  return (user: any) => {
    if (!isAdmin(user)) {
      throw new Error('需要管理员权限');
    }
    
    if (requiredPermission && !hasPermission(user.role, requiredPermission)) {
      throw new Error('权限不足');
    }
    
    return true;
  };
}
