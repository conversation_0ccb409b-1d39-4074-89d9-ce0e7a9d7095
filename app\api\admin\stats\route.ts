import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';
import { withCache } from '../../../../lib/cache';

// 获取真实的仪表板统计数据
async function getDashboardStats() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // 并行查询所有统计数据
  const [
    totalUsers,
    activeUsers,
    totalSubscriptions,
    totalAccounts,
    todayNewUsers,
    todayNewSubscriptions
  ] = await Promise.all([
    // 总用户数
    prisma.user.count(),

    // 活跃用户数（有订阅的用户）
    prisma.user.count({
      where: {
        subscriptions: {
          some: {
            isActive: true
          }
        }
      }
    }),

    // 总订阅数
    prisma.subscription.count(),

    // 总公众号数
    prisma.wechatAccount.count(),

    // 今日新增用户
    prisma.user.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    }),

    // 今日新增订阅
    prisma.subscription.count({
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow
        }
      }
    })
  ]);

  return {
    totalUsers,
    activeUsers,
    totalSubscriptions,
    totalAccounts,
    todayNewUsers,
    todayNewSubscriptions
  };
}

async function handleStatsRequest(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin()(user);

    // 获取统计数据
    const stats = await getDashboardStats();

    return NextResponse.json(stats);
  } catch (error) {
    console.error('获取统计数据失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取统计数据失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 导出带缓存的处理器（5分钟缓存）
export const GET = withCache(handleStatsRequest, 300);
