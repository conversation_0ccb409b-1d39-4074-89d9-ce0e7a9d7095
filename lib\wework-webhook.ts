interface WeWorkMessage {
  msgtype: 'text' | 'markdown' | 'image' | 'news';
  text?: {
    content: string;
    mentioned_list?: string[];
    mentioned_mobile_list?: string[];
  };
  markdown?: {
    content: string;
  };
  image?: {
    base64: string;
    md5: string;
  };
  news?: {
    articles: Array<{
      title: string;
      description?: string;
      url?: string;
      picurl?: string;
    }>;
  };
}

class WeWorkWebhook {
  private webhookUrl: string;

  constructor(webhookUrl: string) {
    this.webhookUrl = webhookUrl;
  }

  // 发送文本消息
  async sendText(content: string, mentionedList?: string[]): Promise<boolean> {
    const message: WeWorkMessage = {
      msgtype: 'text',
      text: {
        content,
        mentioned_list: mentionedList
      }
    };

    return this.sendMessage(message);
  }

  // 发送Markdown消息
  async sendMarkdown(content: string): Promise<boolean> {
    const message: WeWorkMessage = {
      msgtype: 'markdown',
      markdown: {
        content
      }
    };

    return this.sendMessage(message);
  }

  // 发送图片消息
  async sendImage(base64Image: string): Promise<boolean> {
    try {
      // 移除data:image前缀，只保留base64数据
      const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
      
      // 计算MD5
      const crypto = require('crypto');
      const md5 = crypto.createHash('md5').update(base64Data, 'base64').digest('hex');

      const message: WeWorkMessage = {
        msgtype: 'image',
        image: {
          base64: base64Data,
          md5
        }
      };

      return this.sendMessage(message);
    } catch (error) {
      console.error('发送图片消息失败:', error);
      return false;
    }
  }

  // 发送图文消息
  async sendNews(articles: Array<{
    title: string;
    description?: string;
    url?: string;
    picurl?: string;
  }>): Promise<boolean> {
    const message: WeWorkMessage = {
      msgtype: 'news',
      news: {
        articles
      }
    };

    return this.sendMessage(message);
  }

  // 发送微信登录二维码
  async sendWechatLoginQR(qrcode: string, sessionId: string): Promise<boolean> {
    try {
      // 先发送文本说明
      await this.sendMarkdown(`
## 🔐 微信爬虫登录

**会话ID**: \`${sessionId}\`
**状态**: 等待扫码
**有效期**: 5分钟

请使用微信扫描下方二维码完成登录：
      `);

      // 然后发送二维码图片
      return await this.sendImage(qrcode);
    } catch (error) {
      console.error('发送微信登录二维码失败:', error);
      return false;
    }
  }

  // 发送登录状态更新
  async sendLoginStatusUpdate(sessionId: string, status: string, data?: any): Promise<boolean> {
    let content = '';
    let emoji = '';

    switch (status) {
      case 'scanned':
        emoji = '👀';
        content = `## ${emoji} 二维码已扫描\n\n**会话ID**: \`${sessionId}\`\n**状态**: 已扫描，等待确认登录`;
        break;
      case 'confirmed':
        emoji = '✅';
        content = `## ${emoji} 登录成功\n\n**会话ID**: \`${sessionId}\`\n**用户**: ${data?.nickname || '未知用户'}\n**状态**: 登录成功，可以开始爬取文章`;
        break;
      case 'expired':
        emoji = '⏰';
        content = `## ${emoji} 登录超时\n\n**会话ID**: \`${sessionId}\`\n**状态**: 二维码已过期，请重新获取`;
        break;
      case 'failed':
        emoji = '❌';
        content = `## ${emoji} 登录失败\n\n**会话ID**: \`${sessionId}\`\n**状态**: 登录失败，请重试`;
        break;
      default:
        emoji = '📱';
        content = `## ${emoji} 登录状态更新\n\n**会话ID**: \`${sessionId}\`\n**状态**: ${status}`;
    }

    return this.sendMarkdown(content);
  }

  // 发送爬虫任务通知
  async sendCrawlerNotification(type: 'start' | 'success' | 'error', data: any): Promise<boolean> {
    let content = '';

    switch (type) {
      case 'start':
        content = `
## 🚀 开始爬取文章

**公众号**: ${data.accountName}
**任务ID**: \`${data.taskId}\`
**开始时间**: ${new Date().toLocaleString()}
        `;
        break;
      case 'success':
        content = `
## ✅ 文章爬取完成

**公众号**: ${data.accountName}
**任务ID**: \`${data.taskId}\`
**爬取数量**: ${data.articleCount} 篇
**完成时间**: ${new Date().toLocaleString()}
        `;
        break;
      case 'error':
        content = `
## ❌ 文章爬取失败

**公众号**: ${data.accountName}
**任务ID**: \`${data.taskId}\`
**错误信息**: ${data.error}
**失败时间**: ${new Date().toLocaleString()}
        `;
        break;
    }

    return this.sendMarkdown(content);
  }

  // 发送消息的通用方法
  private async sendMessage(message: WeWorkMessage): Promise<boolean> {
    try {
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message)
      });

      const result = await response.json();
      
      if (result.errcode === 0) {
        return true;
      } else {
        console.error('企业微信消息发送失败:', result);
        return false;
      }
    } catch (error) {
      console.error('发送企业微信消息异常:', error);
      return false;
    }
  }

  // 测试webhook连接
  async testConnection(): Promise<boolean> {
    return this.sendText('🔔 FeedWe爬虫服务已连接');
  }
}

export { WeWorkWebhook };
export type { WeWorkMessage };
