const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testConnection() {
  try {
    console.log('🔍 测试数据库连接...');
    
    // 测试基本连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功!');
    
    // 测试查询用户数量
    const userCount = await prisma.user.count();
    console.log(`👥 用户总数: ${userCount}`);
    
    // 测试查询管理员
    const admin = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' },
      select: { email: true, name: true, role: true }
    });
    
    if (admin) {
      console.log('👑 管理员账户:', admin);
    } else {
      console.log('❌ 未找到管理员账户');
    }
    
    // 测试查询公众号数量
    const accountCount = await prisma.wechatAccount.count();
    console.log(`📱 公众号数量: ${accountCount}`);
    
    // 测试查询订阅数量
    const subscriptionCount = await prisma.subscription.count();
    console.log(`📋 订阅数量: ${subscriptionCount}`);
    
    console.log('\n🎉 所有测试通过！本地可以正常连接Vercel的Supabase数据库');
    
  } catch (error) {
    console.error('❌ 连接测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
