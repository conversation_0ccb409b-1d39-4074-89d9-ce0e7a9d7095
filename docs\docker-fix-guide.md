# Docker 构建问题修复指南

## 问题描述

在运行 `docker-compose.crawler.yml` 时遇到以下错误：

```
Error: Cannot find module '/app/docker/crawler-service.js'
```

## 根本原因

1. **路径问题**: Dockerfile 中文件被复制到根目录，但 CMD 命令仍指向子目录
2. **TypeScript 导入问题**: 尝试导入 `.js` 文件，但源文件是 `.ts` 格式
3. **模块解析问题**: Node.js 无法直接运行 TypeScript 文件

## 修复方案

### 1. 修复文件路径

**问题**: 
```dockerfile
COPY docker/crawler-service.js ./
CMD ["node", "docker/crawler-service.js"]  # ❌ 错误路径
```

**修复**:
```dockerfile
COPY docker/crawler-service.js ./
CMD ["npx", "tsx", "crawler-service.js"]  # ✅ 正确路径和运行方式
```

### 2. 修复 TypeScript 导入

**问题**:
```javascript
// crawler-service.js 中的错误导入
const schedulerModule = await import('./lib/wechat-crawler-scheduler.js');  // ❌ 文件不存在
```

**修复**:
```javascript
// 正确导入 TypeScript 文件
const schedulerModule = await import('./lib/wechat-crawler-scheduler.ts');  // ✅ 导入 .ts 文件
```

### 3. 添加 TypeScript 运行时支持

**添加依赖**:
```json
// docker/package.json
{
  "dependencies": {
    "tsx": "^4.20.3",
    "typescript": "^5",
    // ... 其他依赖
  }
}
```

**更新 Dockerfile**:
```dockerfile
# 安装 tsx 用于运行 TypeScript
RUN npm install --production --no-audit --no-fund && \
    npm install tsx --no-audit --no-fund

# 使用 tsx 启动服务
CMD ["npx", "tsx", "crawler-service.js"]
```

## 验证修复

### 1. 运行验证脚本

```bash
node docker/validate-fix.js
```

应该看到所有检查通过：
```
✅ crawler-service.js 导入 TypeScript 文件
✅ package.json 包含 tsx 依赖
✅ Dockerfile 使用 tsx 启动
✅ TypeScript 源文件存在
✅ startup.ts 文件存在
```

### 2. 本地构建测试

```bash
# 确保使用本地构建
# 编辑 docker-compose.crawler.yml，启用 build 配置

# 构建并启动
docker-compose -f docker-compose.crawler.yml up --build -d

# 查看日志
docker-compose -f docker-compose.crawler.yml logs -f
```

### 3. 健康检查

```bash
# 等待服务启动（约30秒）
sleep 30

# 检查健康状态
curl http://localhost:3001/health

# 检查详细状态
curl http://localhost:3001/status
```

## 预期结果

### 成功启动日志

```
🚀 启动 FeedWe 定时任务爬虫服务...
📅 启动时间: 2024-01-01T00:00:00.000Z
🌍 时区: Asia/Shanghai
🔧 环境: production
✅ 模块加载成功
✅ 数据库连接成功
🏥 健康检查服务器启动在端口 3001
✅ FeedWe 定时任务爬虫服务启动完成
🔄 定时任务正在运行中...
```

### 健康检查响应

```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "scheduler": {
    "isRunning": true,
    "isActive": true
  },
  "uptime": 30.5
}
```

## 故障排除

### 1. 如果仍然出现模块找不到错误

```bash
# 进入容器检查文件结构
docker exec -it feedwe-crawler ls -la /app/

# 检查 lib 目录
docker exec -it feedwe-crawler ls -la /app/lib/

# 检查 TypeScript 文件
docker exec -it feedwe-crawler ls -la /app/lib/*.ts
```

### 2. 如果 tsx 无法运行

```bash
# 检查 tsx 是否安装
docker exec -it feedwe-crawler npx tsx --version

# 手动测试 TypeScript 导入
docker exec -it feedwe-crawler node -e "import('./lib/startup.ts').then(console.log).catch(console.error)"
```

### 3. 如果数据库连接失败

```bash
# 检查环境变量
docker exec -it feedwe-crawler env | grep DATABASE_URL

# 测试数据库连接
docker exec -it feedwe-crawler npx prisma db push --preview-feature
```

## 部署建议

### 开发环境

使用本地构建进行开发和测试：

```yaml
# docker-compose.crawler.yml
services:
  feedwe-crawler:
    build:
      context: .
      dockerfile: Dockerfile.crawler
    # ... 其他配置
```

### 生产环境

修复完成后，推送代码触发 GitHub Actions 自动构建：

```bash
git add .
git commit -m "fix: 修复 Docker 构建中的 TypeScript 模块导入问题"
git push origin master
```

然后使用预构建镜像：

```yaml
# docker-compose.crawler.yml
services:
  feedwe-crawler:
    image: registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest
    # ... 其他配置
```

## 性能影响

使用 `tsx` 运行 TypeScript 的性能影响：

- **启动时间**: 增加约 1-2 秒（TypeScript 编译时间）
- **内存使用**: 增加约 20-30MB（tsx 运行时）
- **CPU 使用**: 启动时稍高，运行时无明显影响

对于定时任务服务来说，这些影响是可以接受的。

## 替代方案

如果希望避免运行时 TypeScript 编译，可以考虑：

1. **预编译方案**: 在 Dockerfile 中编译 TypeScript 为 JavaScript
2. **构建时编译**: 在 GitHub Actions 中预编译
3. **混合方案**: 关键模块预编译，其他模块运行时编译

当前的 `tsx` 方案是最简单且可靠的解决方案。
