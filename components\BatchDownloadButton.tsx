'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/app/components/AuthProvider';

interface BatchDownloadButtonProps {
  articleIds: string[];
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  downloadToLocal?: boolean; // 是否下载到本地
  onDownloadStart?: () => void;
  onDownloadComplete?: () => void;
}

export function BatchDownloadButton({
  articleIds,
  className,
  variant = 'default',
  size = 'default',
  downloadToLocal = true, // 默认下载到本地
  onDownloadStart,
  onDownloadComplete
}: BatchDownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const { token } = useAuth();

  const handleBatchDownload = async () => {
    if (isDownloading || articleIds.length === 0) return;

    if (!token) {
      toast.error('请先登录');
      return;
    }

    const maxArticles = downloadToLocal ? 20 : 50;
    if (articleIds.length > maxArticles) {
      toast.error(`单次最多下载${maxArticles}篇文章${downloadToLocal ? '到本地' : ''}`);
      return;
    }

    setIsDownloading(true);
    onDownloadStart?.();

    try {
      if (downloadToLocal) {
        // 下载到本地（ZIP文件）
        const response = await fetch('/api/articles/download-local', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            articleIds,
            options: {
              withCredentials: true,
              timeout: 30
            }
          }),
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || '批量下载失败');
        }

        // 下载ZIP文件
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 从响应头获取文件名
        const contentDisposition = response.headers.get('content-disposition');
        const fileNameMatch = contentDisposition?.match(/filename="(.+)"/);
        const fileName = fileNameMatch ? fileNameMatch[1] : `wechat_articles_${Date.now()}.zip`;

        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast.success(`已下载 ${articleIds.length} 篇文章到本地`);
      } else {
        // 保存到服务器
        const response = await fetch('/api/articles/download', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            articleIds,
            options: {
              withCredentials: true,
              timeout: 30
            }
          }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '批量下载失败');
        }

        toast.success(`批量下载任务已创建，共 ${data.totalCount} 篇文章`);
      }

      onDownloadComplete?.();
    } catch (error) {
      console.error('批量下载失败:', error);
      toast.error(error instanceof Error ? error.message : '批量下载失败');
    } finally {
      setIsDownloading(false);
    }
  };

  if (articleIds.length === 0) {
    return null;
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleBatchDownload}
      disabled={isDownloading}
      className={className}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
      <span className="ml-2">
        {isDownloading
          ? (downloadToLocal ? '打包下载中...' : '批量下载中...')
          : `${downloadToLocal ? '打包下载' : '批量下载'} (${articleIds.length})`
        }
      </span>
    </Button>
  );
}
