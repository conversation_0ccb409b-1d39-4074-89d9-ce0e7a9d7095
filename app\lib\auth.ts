import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '../../lib/prisma';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  inviteCode?: string;
}

// 生成JWT token
export function generateToken(user: AuthUser): string {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
}

// 验证JWT token
export function verifyToken(token: string): AuthUser | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    return null;
  }
}

// 哈希密码
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

// 验证密码
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// 生成邀请码
export function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 验证邀请码
export async function validateInviteCode(inviteCode: string): Promise<{ valid: boolean; inviter?: AuthUser }> {
  try {
    const inviter = await prisma.user.findUnique({
      where: { inviteCode },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        inviteCode: true,
      }
    });

    return {
      valid: !!inviter,
      inviter: inviter ? {
        id: inviter.id,
        email: inviter.email,
        name: inviter.name || undefined,
        avatar: inviter.avatar || undefined,
        inviteCode: inviter.inviteCode || undefined,
      } : undefined
    };
  } catch (error) {
    return { valid: false };
  }
}

// 从请求头获取用户信息
export async function getUserFromRequest(request: Request): Promise<AuthUser | null> {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const decoded = verifyToken(token);
  
  if (!decoded) {
    return null;
  }

  // 验证用户是否仍然存在且活跃
  try {
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        inviteCode: true,
        isActive: true,
      }
    });

    if (!user || !user.isActive) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      avatar: user.avatar || undefined,
      inviteCode: user.inviteCode || undefined,
    };
  } catch (error) {
    return null;
  }
}

// 创建邀请奖励记录
export async function createInviteReward(inviterId: string, inviteeId: string, orderId: string, amount: number) {
  try {
    const rewardAmount = amount * 0.5; // 50% 返现
    
    await prisma.inviteReward.create({
      data: {
        inviterId,
        inviteeId,
        orderId,
        amount: rewardAmount,
        status: 'pending'
      }
    });
  } catch (error) {
    console.error('创建邀请奖励失败:', error);
  }
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证密码强度
export function isValidPassword(password: string): { valid: boolean; message?: string } {
  if (password.length < 6) {
    return { valid: false, message: '密码至少需要6个字符' };
  }
  
  if (password.length > 50) {
    return { valid: false, message: '密码不能超过50个字符' };
  }
  
  return { valid: true };
}
