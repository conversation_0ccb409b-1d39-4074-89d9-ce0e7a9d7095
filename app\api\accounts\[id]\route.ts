import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

// 获取单个公众号信息
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: accountId } = await params;

  if (!accountId) {
    return NextResponse.json({ error: 'Account ID required' }, { status: 400 });
  }

  try {
    const account = await prisma.wechatAccount.findUnique({
      where: { id: accountId }
    });

    if (!account) {
      return NextResponse.json({ error: 'Account not found' }, { status: 404 });
    }

    return NextResponse.json(account);
  } catch (error) {
    console.error('获取公众号信息失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
