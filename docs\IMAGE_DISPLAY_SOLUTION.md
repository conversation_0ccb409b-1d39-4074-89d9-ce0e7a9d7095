# 下载文章图片显示问题解决方案

## 问题描述

下载的HTML文件中图片不显示的原因：
1. **微信图片防盗链**：微信图片URL有防盗链保护，在本地HTML文件中无法直接访问
2. **缺少图片资源**：只下载了HTML内容，没有下载图片资源到本地

## 解决方案：代理URL方案（推荐）⭐

**特点**：
- ✅ 文件小，下载快（通常 < 100KB）
- ✅ 图片质量高，无损显示
- ✅ 实现简单，维护成本低
- ❌ 需要网络连接查看图片

**实现原理**：
- 将HTML中的微信图片URL替换为网站的代理URL
- 通过 `/api/proxy/wechat-image` 接口代理访问微信图片
- 在HTML中添加图片显示说明，提醒用户需要网络连接

**使用方法**：
```tsx
import { EnhancedDownloadButton } from '@/components/EnhancedDownloadButton';

// 使用代理URL方案的下载按钮
<EnhancedDownloadButton
  articleId="文章ID"
  articleTitle="文章标题"
/>
```

## API接口

### 文章下载（代理URL方案）
```
POST /api/articles/download
Content-Type: application/json

{
  "articleId": "文章ID",
  "options": {
    "withCredentials": true,
    "timeout": 30
  }
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "文章下载成功",
  "downloadId": "下载记录ID",
  "article": {
    "id": "文章ID",
    "title": "文章标题",
    "html": "处理后的HTML内容（包含代理URL）",
    "size": 12345
  }
}
```

## 技术实现

### 图片URL处理（代理URL方案）

```typescript
// 替换img标签中的src属性
content = content.replace(/<img([^>]*)\s+src=["']([^"']+)["']([^>]*)>/gi, (match, before, src, after) => {
  const isWechatImage = wechatImageDomains.some(domain => src.includes(domain));

  if (isWechatImage && !src.startsWith('/api/proxy/wechat-image')) {
    const proxyUrl = `/api/proxy/wechat-image?url=${encodeURIComponent(src)}`;
    return `<img${before} src="${proxyUrl}"${after}>`;
  }

  return match;
});

// 替换CSS中的background-image
content = content.replace(/background-image:\s*url\(["']?([^"')]+)["']?\)/gi, (match, url) => {
  const isWechatImage = wechatImageDomains.some(domain => url.includes(domain));

  if (isWechatImage && !url.startsWith('/api/proxy/wechat-image')) {
    const proxyUrl = `/api/proxy/wechat-image?url=${encodeURIComponent(url)}`;
    return `background-image: url("${proxyUrl}")`;
  }

  return match;
});
```

### 微信图片代理接口

```typescript
// /api/proxy/wechat-image
export async function GET(request: NextRequest) {
  const imageUrl = searchParams.get('url');

  // 验证微信域名
  const allowedDomains = ['mmbiz.qpic.cn', 'mmbiz.qlogo.cn', 'wx.qlogo.cn'];

  // 使用适当的请求头绕过防盗链
  const response = await fetch(imageUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15',
      'Referer': 'https://mp.weixin.qq.com/',
      'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
    }
  });

  return new NextResponse(imageBuffer, {
    headers: {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=86400' // 缓存1天
    }
  });
}
```

## 使用建议

### 适用场景

**代理URL方案适合**：
- 日常阅读和分享
- 网络环境良好的用户
- 需要快速下载的场景
- 存储空间有限的情况
- 大部分使用场景（推荐）

### 性能特点

**代理URL方案**：
- 下载时间：1-3秒
- 文件大小：通常 < 100KB
- 网络要求：查看图片时需要网络连接
- 图片质量：原始质量，无损显示
- 维护成本：低，只需要维护代理接口

## 故障排除

### 图片仍然不显示

1. **检查网络连接**（在线版本）
   - 确保能够访问网站的代理接口
   - 检查防火墙设置

2. **检查代理服务器**
   - 访问 `/admin/proxy-servers` 检查代理服务器状态
   - 确保至少有一个可用的代理服务器

3. **重新下载**
   - 尝试重新下载文章
   - 选择不同的下载模式

### 下载失败

1. **检查权限**
   - 确保已登录且订阅了相应公众号
   - 检查文章是否仍然存在

2. **检查网络**
   - 确保网络连接稳定
   - 代理服务器可正常访问

3. **联系管理员**
   - 查看服务器日志
   - 检查代理服务器配置

## 更新日志

- **v1.0** - 基础下载功能
- **v1.1** - 添加图片代理支持（在线版本）
- **v1.2** - 添加完全离线下载支持
- **v1.3** - 增强下载按钮组件，支持模式选择

---

**注意**：离线版本下载时间较长，请耐心等待。建议在网络环境良好时使用离线下载功能。
