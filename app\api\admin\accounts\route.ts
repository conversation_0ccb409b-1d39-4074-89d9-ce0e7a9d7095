import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';
import { processWechatImageUrl } from '../../../../lib/wechat-image-proxy';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';

    // 构建查询条件
    const whereCondition: any = {};

    // 搜索条件
    if (search) {
      whereCondition.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // 获取公众号总数
    const totalAccounts = await prisma.wechatAccount.count({
      where: whereCondition
    });

    // 获取分页公众号数据
    const accounts = await prisma.wechatAccount.findMany({
      where: whereCondition,
      include: {
        _count: {
          select: {
            subscriptions: true,
            articles: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    // 转换数据格式，处理微信图片URL
    const formattedAccounts = accounts.map(account => {
      return {
        id: account.id,
        name: account.name,
        avatar: processWechatImageUrl(account.avatar),
        description: account.description,
        openid: account.openid,
        createdAt: account.createdAt.toISOString(),
        updatedAt: account.updatedAt.toISOString(),
        subscriptionCount: account._count.subscriptions,
        articleCount: account._count.articles,
        // 爬取相关字段
        fakeid: account.fakeid,
        enableCrawling: account.enableCrawling,
        lastCrawlTime: account.lastCrawlTime?.toISOString(),
        crawlStatus: account.crawlStatus,
        crawlError: account.crawlError
      };
    });

    const totalPages = Math.ceil(totalAccounts / limit);

    return NextResponse.json({
      accounts: formattedAccounts,
      totalPages,
      currentPage: page,
      totalAccounts
    });
  } catch (error) {
    console.error('获取公众号列表失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取公众号列表失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
