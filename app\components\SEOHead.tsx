'use client';

import Head from 'next/head';
import { SEOConfig } from '../../lib/seo';

interface SEOHeadProps {
  config: SEOConfig;
}

export default function SEOHead({ config }: SEOHeadProps) {
  return (
    <Head>
      <title>{config.title}</title>
      <meta name="description" content={config.description} />
      {config.keywords && (
        <meta name="keywords" content={config.keywords.join(', ')} />
      )}
      
      {/* Open Graph */}
      <meta property="og:title" content={config.title} />
      <meta property="og:description" content={config.description} />
      <meta property="og:type" content={config.type || 'website'} />
      {config.url && <meta property="og:url" content={config.url} />}
      {config.image && <meta property="og:image" content={config.image} />}
      <meta property="og:locale" content="zh_CN" />
      <meta property="og:site_name" content="FeedWe" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={config.title} />
      <meta name="twitter:description" content={config.description} />
      {config.image && <meta name="twitter:image" content={config.image} />}
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
      
      {/* 搜索引擎优化 */}
      <meta name="robots" content="index, follow" />
      <meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1" />
      
      {/* 语言和地区 */}
      <meta httpEquiv="content-language" content="zh-CN" />
      <link rel="alternate" hrefLang="zh-CN" href={config.url} />
      
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "FeedWe",
            "description": config.description,
            "url": config.url,
            "applicationCategory": "ProductivityApplication",
            "operatingSystem": "Web",
            "offers": {
              "@type": "Offer",
              "price": "2",
              "priceCurrency": "CNY",
              "priceSpecification": {
                "@type": "UnitPriceSpecification",
                "price": "2",
                "priceCurrency": "CNY",
                "unitText": "月"
              }
            },
            "author": {
              "@type": "Organization",
              "name": "FeedWe Team"
            }
          })
        }}
      />
    </Head>
  );
}
