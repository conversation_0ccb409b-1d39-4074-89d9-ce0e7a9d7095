import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.SUBSCRIPTION_MANAGEMENT)(user);

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';

    // 构建查询条件
    const whereCondition: any = {};

    // 搜索条件
    if (search) {
      whereCondition.OR = [
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          wechatAccount: {
            name: {
              contains: search,
              mode: 'insensitive'
            }
          }
        }
      ];
    }

    // 状态筛选
    if (status !== 'all') {
      whereCondition.isActive = status === 'active';
    }

    // 获取订阅总数
    const totalSubscriptions = await prisma.subscription.count({
      where: whereCondition
    });

    // 获取分页订阅数据
    const subscriptions = await prisma.subscription.findMany({
      where: whereCondition,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true
          }
        },
        wechatAccount: {
          select: {
            id: true,
            name: true,
            avatar: true,
            description: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    // 转换数据格式
    const formattedSubscriptions = subscriptions.map(subscription => ({
      id: subscription.id,
      userId: subscription.userId,
      userEmail: subscription.user.email,
      userName: subscription.user.name,
      wechatAccount: subscription.wechatAccount,
      createdAt: subscription.createdAt.toISOString(),
      isActive: subscription.isActive,
      lastSyncAt: subscription.updatedAt.toISOString() // 暂时使用updatedAt
    }));

    const totalPages = Math.ceil(totalSubscriptions / limit);

    return NextResponse.json({
      subscriptions: formattedSubscriptions,
      totalPages,
      currentPage: page,
      totalSubscriptions
    });
  } catch (error) {
    console.error('获取订阅列表失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取订阅列表失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 添加新订阅
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);

    // 检查管理员权限
    requireAdmin(Permission.SUBSCRIPTION_MANAGEMENT)(user);

    const { userId, accountId } = await request.json();

    if (!userId || !accountId) {
      return NextResponse.json(
        { error: '用户ID和公众号ID不能为空' },
        { status: 400 }
      );
    }

    // 检查用户是否存在
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    // 检查公众号是否存在
    const wechatAccount = await prisma.wechatAccount.findUnique({
      where: { id: accountId }
    });

    if (!wechatAccount) {
      return NextResponse.json(
        { error: '公众号不存在' },
        { status: 404 }
      );
    }

    // 检查是否已经存在订阅
    const existingSubscription = await prisma.subscription.findUnique({
      where: {
        userId_wechatAccountId: {
          userId,
          wechatAccountId: accountId
        }
      }
    });

    if (existingSubscription) {
      return NextResponse.json(
        { error: '该用户已经订阅了这个公众号' },
        { status: 400 }
      );
    }

    // 创建新订阅
    const newSubscription = await prisma.subscription.create({
      data: {
        userId,
        wechatAccountId: accountId,
        isActive: true
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true
          }
        },
        wechatAccount: {
          select: {
            id: true,
            name: true,
            avatar: true,
            description: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      subscription: newSubscription,
      message: '订阅添加成功'
    });
  } catch (error) {
    console.error('添加订阅失败:', error);
    const errorMessage = error instanceof Error ? error.message : '添加订阅失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
