# 微信公众号文章下载功能

## 功能概述

本项目新增了微信公众号文章下载功能，允许用户下载已订阅的公众号文章，支持单篇下载和批量下载。

## 🚀 主要特性

- ✅ **单篇文章下载** - 在文章详情页点击下载按钮
- ✅ **批量文章下载** - 选择多篇文章一次性下载（最多50篇）
- ✅ **多代理服务器支持** - 配置多个代理服务器确保下载稳定性
- ✅ **自动重试机制** - 下载失败时自动重试
- ✅ **下载状态跟踪** - 实时查看下载进度和状态
- ✅ **权限验证** - 仅订阅用户可下载对应公众号文章
- ✅ **HTML格式保存** - 保存完整的文章HTML内容
- ✅ **资源完整性验证** - 验证下载内容的完整性

## 📦 新增数据库表

### ProxyServer (代理服务器)
- 存储代理服务器配置信息
- 支持优先级排序和状态管理
- 配置重试次数和超时时间

### ArticleDownload (文章下载记录)
- 记录每次下载的详细信息
- 跟踪下载状态和进度
- 关联用户、文章和代理服务器

## 🛠 安装和配置

### 1. 数据库迁移
```bash
npx prisma migrate dev --name add_article_download_feature
```

### 2. 安装依赖
```bash
npm install jszip mime-types sonner
npm install --save-dev @types/mime-types
```

### 3. 初始化代理服务器
```bash
npx tsx scripts/init-proxy-servers.ts
```

### 4. 测试功能
```bash
npx tsx scripts/test-download-feature.ts
```

## 🎯 使用方法

### 用户端使用

1. **单篇文章下载**
   - 进入文章详情页
   - 点击下载按钮
   - 系统会创建下载任务并异步执行

2. **批量文章下载**
   - 在文章列表页选择多篇文章
   - 点击批量下载按钮
   - 系统会创建批量下载任务

3. **查看下载状态**
   - 访问下载记录页面
   - 查看下载进度和状态
   - 支持重新下载失败的文章

### 管理员配置

1. **代理服务器管理**
   - 访问 `/admin/proxy-servers` 页面
   - 添加、编辑、删除代理服务器
   - 设置优先级和重试参数

2. **系统配置**
   - 在系统配置中添加微信凭据
   - 配置 `wechat_token`、`wechat_cookie`、`wechat_data_ticket`

## 📡 API 接口

### 下载相关接口

- `POST /api/articles/download` - 下载单篇文章
- `PUT /api/articles/download` - 批量下载文章
- `GET /api/articles/downloads` - 获取下载记录
- `GET /api/articles/downloads/[id]` - 获取单个下载记录
- `POST /api/articles/downloads/[id]` - 重新下载
- `DELETE /api/articles/downloads/[id]` - 删除下载记录

### 代理服务器管理接口（仅管理员）

- `GET /api/admin/proxy-servers` - 获取代理服务器列表
- `POST /api/admin/proxy-servers` - 创建代理服务器
- `PUT /api/admin/proxy-servers/[id]` - 更新代理服务器
- `DELETE /api/admin/proxy-servers/[id]` - 删除代理服务器

## 🧩 前端组件

### DownloadButton
单篇文章下载按钮组件
```tsx
<DownloadButton
  articleId="文章ID"
  articleTitle="文章标题"
  variant="outline"
  size="sm"
/>
```

### BatchDownloadButton
批量下载按钮组件
```tsx
<BatchDownloadButton
  articleIds={["文章ID1", "文章ID2"]}
  onDownloadStart={() => console.log('开始下载')}
  onDownloadComplete={() => console.log('下载完成')}
/>
```

### DownloadStatus
下载状态显示组件
```tsx
<DownloadStatus status="COMPLETED" />
```

## 🔧 技术实现

### 下载流程

1. **权限验证** - 检查用户是否订阅了该公众号
2. **重复检查** - 避免重复下载同一篇文章
3. **代理选择** - 按优先级选择可用的代理服务器
4. **下载执行** - 使用代理下载文章HTML
5. **内容验证** - 验证下载的HTML是否完整
6. **状态更新** - 更新下载记录状态
7. **错误处理** - 记录错误信息并支持重试

### 核心类

- `ArticleDownloader` - 文章下载核心类
- 支持代理轮换和自动重试
- 内置HTML完整性验证
- 异步下载和状态管理

## ⚠️ 注意事项

1. **权限控制** - 只有订阅了相应公众号的用户才能下载文章
2. **频率限制** - 批次间有延迟，避免请求过于频繁
3. **文件存储** - 需要实现文件存储逻辑（本地或云存储）
4. **代理稳定性** - 建议配置多个代理服务器确保可用性
5. **微信凭据** - 需要配置有效的微信凭据才能正常下载

## �️ 图片显示解决方案

### 问题说明
下载的HTML文件中图片可能不显示，这是因为微信图片有防盗链保护。

### 解决方案

#### 方案1：在线版本（推荐）⭐
- **特点**：文件小，下载快，需要网络查看图片
- **原理**：将图片URL替换为代理URL
- **适用**：日常阅读，快速下载

#### 方案2：离线版本（完全离线）🔥
- **特点**：完全离线，文件大，下载慢
- **原理**：下载图片并转为base64嵌入HTML
- **适用**：长期保存，离线阅读

### 使用方法

```tsx
import { EnhancedDownloadButton } from '@/components/EnhancedDownloadButton';

// 增强下载按钮（支持选择下载模式）
<EnhancedDownloadButton
  articleId="文章ID"
  articleTitle="文章标题"
  showModeSelector={true}
/>
```

### API接口

- `POST /api/articles/download` - 在线版本下载
- `POST /api/articles/download-offline` - 离线版本下载

详细说明请查看：[图片显示解决方案文档](docs/IMAGE_DISPLAY_SOLUTION.md)

## 🔮 未来扩展

- PDF格式导出
- 下载进度实时推送
- 下载统计和分析
- 批量离线下载优化

## 📞 技术支持

如有问题，请查看：
1. 运行测试脚本检查配置
2. 查看服务器日志
3. 检查代理服务器状态
4. 验证微信凭据配置

---

**注意**: 此功能需要配合有效的代理服务器和微信凭据才能正常工作。请确保在生产环境中正确配置这些参数。
