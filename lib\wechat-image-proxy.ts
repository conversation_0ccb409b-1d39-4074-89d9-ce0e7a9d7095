/**
 * 微信图片代理工具函数
 * 统一处理微信图片URL的代理逻辑
 */

/**
 * 检查是否为微信图片URL
 */
export function isWechatImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  
  const wechatImageDomains = [
    'mmbiz.qpic.cn',
    'mmbiz.qlogo.cn', 
    'wx.qlogo.cn',
    'thirdwx.qlogo.cn',
    'mp.weixin.qq.com'
  ];

  return wechatImageDomains.some(domain => url.includes(domain));
}

/**
 * 将微信图片URL转换为代理URL
 */
export function getProxiedWechatImageUrl(originalUrl: string): string {
  if (!originalUrl || !isWechatImageUrl(originalUrl)) {
    return originalUrl;
  }

  return `/api/proxy/wechat-image?url=${encodeURIComponent(originalUrl)}`;
}

/**
 * 处理对象中的微信图片URL
 */
export function processWechatImageUrl(url: string | null | undefined): string | null | undefined {
  if (!url) return url;
  return isWechatImageUrl(url) ? getProxiedWechatImageUrl(url) : url;
}
