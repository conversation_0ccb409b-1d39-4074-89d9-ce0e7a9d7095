/**
 * 北京时间工具函数
 * 统一处理项目中的时间相关操作，确保使用北京时间
 */

/**
 * 获取当前北京时间
 * @returns Date 北京时间的Date对象
 */
export function getBeijingTime(): Date {
  const now = new Date();
  // 获取UTC时间，然后加8小时得到北京时间
  const utcTime = now.getTime() + (now.getTimezoneOffset() * 60 * 1000);
  const beijingTime = new Date(utcTime + (8 * 60 * 60 * 1000));
  return beijingTime;
}

/**
 * 将任意时间转换为北京时间
 * @param date 要转换的时间，如果不提供则使用当前时间
 * @returns Date 北京时间的Date对象
 */
export function toBeijingTime(date?: Date): Date {
  const targetDate = date || new Date();
  // 获取UTC时间，然后加8小时得到北京时间
  const utcTime = targetDate.getTime() + (targetDate.getTimezoneOffset() * 60 * 1000);
  return new Date(utcTime + (8 * 60 * 60 * 1000));
}

/**
 * 格式化北京时间为字符串
 * @param date 要格式化的时间，如果不提供则使用当前时间
 * @param options 格式化选项
 * @returns string 格式化后的时间字符串
 */
export function formatBeijingTime(date?: Date, options?: {
  includeSeconds?: boolean;
  includeDate?: boolean;
  separator?: string;
}): string {
  const targetDate = date || new Date();
  const opts = {
    includeSeconds: true,
    includeDate: true,
    separator: ' ',
    ...options
  };

  // 使用toLocaleString直接获取北京时间字符串
  const beijingTimeStr = targetDate.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: opts.includeSeconds ? '2-digit' : undefined,
    hour12: false
  });

  if (!opts.includeDate) {
    // 只返回时间部分
    const timePart = beijingTimeStr.split(' ')[1];
    return opts.includeSeconds ? timePart : timePart.substring(0, 5);
  }

  return beijingTimeStr.replace(/\//g, '-').replace(',', opts.separator);
}

/**
 * 检查是否在允许的时间范围内（北京时间6:00-23:00）
 * @param startHour 开始小时，默认6
 * @param endHour 结束小时，默认23
 * @returns boolean 是否在允许的时间范围内
 */
export function isInAllowedTimeRange(startHour: number = 6, endHour: number = 23): boolean {
  const beijingTime = new Date();
  const hour = beijingTime.getHours();
  return hour >= startHour && hour < endHour;
}

/**
 * 获取北京时间的小时数
 * @param date 要获取小时的时间，如果不提供则使用当前北京时间
 * @returns number 小时数（0-23）
 */
export function getBeijingHour(date?: Date): number {
  const beijingTime = date || getBeijingTime();
  return beijingTime.getHours();
}

/**
 * 创建北京时间的Date对象
 * @param year 年
 * @param month 月（1-12）
 * @param day 日
 * @param hour 小时，默认0
 * @param minute 分钟，默认0
 * @param second 秒，默认0
 * @returns Date 北京时间的Date对象
 */
export function createBeijingTime(
  year: number,
  month: number,
  day: number,
  hour: number = 0,
  minute: number = 0,
  second: number = 0
): Date {
  // 注意：Date构造函数的月份是0-11，所以需要减1
  const date = new Date(year, month - 1, day, hour, minute, second);
  // 调整为北京时间（UTC+8）
  return new Date(date.getTime() - (8 * 60 * 60 * 1000));
}

/**
 * 计算两个时间之间的差值（毫秒）
 * @param date1 第一个时间
 * @param date2 第二个时间，如果不提供则使用当前北京时间
 * @returns number 时间差值（毫秒）
 */
export function getTimeDifference(date1: Date, date2?: Date): number {
  const time2 = date2 || getBeijingTime();
  return Math.abs(time2.getTime() - date1.getTime());
}

/**
 * 检查时间是否超过指定的分钟数
 * @param date 要检查的时间
 * @param minutes 分钟数
 * @param referenceTime 参考时间，如果不提供则使用当前北京时间
 * @returns boolean 是否超过指定分钟数
 */
export function isTimeExceeded(date: Date, minutes: number, referenceTime?: Date): boolean {
  const reference = referenceTime || getBeijingTime();
  const diffMs = getTimeDifference(date, reference);
  const diffMinutes = diffMs / (60 * 1000);
  return diffMinutes >= minutes;
}

/**
 * 获取北京时间的ISO字符串
 * @param date 要转换的时间，如果不提供则使用当前北京时间
 * @returns string ISO格式的时间字符串
 */
export function getBeijingISOString(date?: Date): string {
  const beijingTime = date || getBeijingTime();
  return beijingTime.toISOString();
}
