'use client';

import { useEffect } from 'react';
import <PERSON>ript from 'next/script';

export default function Analytics() {
  const GA_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS;
  const BAIDU_ID = process.env.NEXT_PUBLIC_BAIDU_ANALYTICS;

  useEffect(() => {
    // 百度统计
    if (BAIDU_ID) {
      const script = document.createElement('script');
      script.innerHTML = `
        var _hmt = _hmt || [];
        (function() {
          var hm = document.createElement("script");
          hm.src = "https://hm.baidu.com/hm.js?${BAIDU_ID}";
          var s = document.getElementsByTagName("script")[0]; 
          s.parentNode.insertBefore(hm, s);
        })();
      `;
      document.head.appendChild(script);
    }
  }, [BAIDU_ID]);

  if (!GA_ID && !BAIDU_ID) return null;

  return (
    <>
      {/* Google Analytics */}
      {GA_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${GA_ID}', {
                page_title: document.title,
                page_location: window.location.href,
              });
            `}
          </Script>
        </>
      )}

      {/* 百度统计已在useEffect中处理 */}
    </>
  );
}
