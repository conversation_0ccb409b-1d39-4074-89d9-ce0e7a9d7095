import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';

/**
 * 修复数据库中错误的头像URL
 * 将代理URL转换回原始URL
 */
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    console.log('🔧 开始修复头像URL...');

    // 查找所有包含代理URL的记录
    const accountsWithProxyUrls = await prisma.wechatAccount.findMany({
      where: {
        avatar: {
          contains: '/api/proxy/wechat-image?url='
        }
      }
    });

    console.log(`📊 找到 ${accountsWithProxyUrls.length} 个需要修复的记录`);

    let fixedCount = 0;
    const fixResults = [];

    for (const account of accountsWithProxyUrls) {
      try {
        // 从代理URL中提取原始URL
        const proxyUrl = account.avatar;
        const urlMatch = proxyUrl?.match(/\/api\/proxy\/wechat-image\?url=(.+)$/);
        
        if (urlMatch && urlMatch[1]) {
          const originalUrl = decodeURIComponent(urlMatch[1]);
          
          console.log(`🔄 修复公众号 ${account.name}:`);
          console.log(`   原URL: ${proxyUrl}`);
          console.log(`   新URL: ${originalUrl}`);

          // 更新数据库
          await prisma.wechatAccount.update({
            where: { id: account.id },
            data: { avatar: originalUrl }
          });

          fixedCount++;
          fixResults.push({
            id: account.id,
            name: account.name,
            oldUrl: proxyUrl,
            newUrl: originalUrl
          });
        }
      } catch (error) {
        console.error(`❌ 修复公众号 ${account.name} 失败:`, error);
        fixResults.push({
          id: account.id,
          name: account.name,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    console.log(`✅ 修复完成，共修复 ${fixedCount} 个记录`);

    return NextResponse.json({
      success: true,
      message: `成功修复 ${fixedCount} 个头像URL`,
      totalFound: accountsWithProxyUrls.length,
      fixedCount,
      results: fixResults
    });

  } catch (error) {
    console.error('修复头像URL失败:', error);
    const errorMessage = error instanceof Error ? error.message : '修复失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

/**
 * 检查需要修复的记录数量
 */
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    // 统计需要修复的记录
    const count = await prisma.wechatAccount.count({
      where: {
        avatar: {
          contains: '/api/proxy/wechat-image?url='
        }
      }
    });

    // 获取示例记录
    const examples = await prisma.wechatAccount.findMany({
      where: {
        avatar: {
          contains: '/api/proxy/wechat-image?url='
        }
      },
      select: {
        id: true,
        name: true,
        avatar: true
      },
      take: 5
    });

    return NextResponse.json({
      needsFixCount: count,
      examples: examples.map(account => ({
        id: account.id,
        name: account.name,
        currentUrl: account.avatar,
        extractedUrl: account.avatar?.match(/\/api\/proxy\/wechat-image\?url=(.+)$/)?.[1] 
          ? decodeURIComponent(account.avatar.match(/\/api\/proxy\/wechat-image\?url=(.+)$/)?.[1] || '')
          : null
      }))
    });

  } catch (error) {
    console.error('检查头像URL失败:', error);
    const errorMessage = error instanceof Error ? error.message : '检查失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
