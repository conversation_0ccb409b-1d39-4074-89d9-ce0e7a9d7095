#!/bin/bash

# 快速测试 Docker 构建和部署

set -e

echo "🧪 FeedWe Docker 快速测试"
echo "=========================="

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请启动 Docker Desktop"
    exit 1
fi

echo "✅ Docker 正在运行"

# 验证修复
echo "🔍 验证修复..."
node docker/validate-fix.js

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose -f docker-compose.crawler.yml down || true

# 构建新镜像
echo "🔨 构建 Docker 镜像..."
docker-compose -f docker-compose.crawler.yml build

# 启动服务
echo "🚀 启动服务..."
docker-compose -f docker-compose.crawler.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动（30秒）..."
sleep 30

# 检查容器状态
echo "📊 检查容器状态..."
docker-compose -f docker-compose.crawler.yml ps

# 检查日志
echo "📋 最近的日志:"
docker-compose -f docker-compose.crawler.yml logs --tail=20

# 健康检查
echo "🏥 健康检查..."
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ 健康检查通过"
    
    echo "📊 服务状态:"
    curl -s http://localhost:3001/status | python3 -m json.tool 2>/dev/null || curl -s http://localhost:3001/status
    
    echo ""
    echo "🎉 测试成功！"
    echo "🌐 健康检查: http://localhost:3001/health"
    echo "📊 状态查询: http://localhost:3001/status"
    
else
    echo "❌ 健康检查失败"
    echo "📋 详细日志:"
    docker-compose -f docker-compose.crawler.yml logs
    exit 1
fi

echo ""
echo "🔧 管理命令:"
echo "  查看日志: docker-compose -f docker-compose.crawler.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.crawler.yml down"
echo "  重启服务: docker-compose -f docker-compose.crawler.yml restart"
