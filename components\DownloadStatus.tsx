'use client';

import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Download, XCircle } from 'lucide-react';
import { DownloadStatus as Status } from '@/app/types';

interface DownloadStatusProps {
  status: Status;
  className?: string;
}

export function DownloadStatus({ status, className }: DownloadStatusProps) {
  const getStatusConfig = (status: Status) => {
    switch (status) {
      case 'PENDING':
        return {
          label: '等待中',
          variant: 'secondary' as const,
          icon: Clock
        };
      case 'DOWNLOADING':
        return {
          label: '下载中',
          variant: 'default' as const,
          icon: Download
        };
      case 'COMPLETED':
        return {
          label: '已完成',
          variant: 'success' as const,
          icon: CheckCircle
        };
      case 'FAILED':
        return {
          label: '失败',
          variant: 'destructive' as const,
          icon: XCircle
        };
      default:
        return {
          label: '未知',
          variant: 'secondary' as const,
          icon: Clock
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className={className}>
      <Icon className="h-3 w-3 mr-1" />
      {config.label}
    </Badge>
  );
}
