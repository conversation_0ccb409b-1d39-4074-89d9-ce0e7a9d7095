# Docker 构建故障排除指南

## 常见构建错误及解决方案

### 1. npm ci 失败

**错误信息**:
```
ERROR: failed to solve: process "/bin/sh -c npm ci --only=production" did not complete successfully: exit code 1
```

**解决方案**:
- ✅ 已修复：使用专门的 `docker/package.json` 文件，只包含必要的生产依赖
- ✅ 已修复：使用 `npm install --production` 替代 `npm ci --only=production`
- ✅ 已修复：添加必要的系统依赖 (python3, make, g++)

### 2. Prisma 生成失败

**错误信息**:
```
Error: Cannot find module '@prisma/client'
```

**解决方案**:
- ✅ 已修复：确保在安装依赖后运行 `npx prisma generate`
- ✅ 已修复：在 Docker 镜像中包含 `prisma` 包

### 3. 模块导入失败

**错误信息**:
```
Cannot find module '../lib/xxx'
```

**解决方案**:
- ✅ 已修复：在 Dockerfile 中正确复制 `lib/` 目录
- ✅ 已修复：使用 `.dockerignore` 确保必要文件被包含

## 本地测试构建

### 快速测试
```bash
# 运行测试构建脚本
chmod +x docker/test-build.sh
./docker/test-build.sh
```

### 手动测试
```bash
# 构建测试镜像
docker build -f Dockerfile.crawler -t test-crawler .

# 运行测试容器
docker run --rm -it \
  -p 3001:3001 \
  -e DATABASE_URL="mysql://test:test@localhost:3306/test" \
  -e DISABLE_CRAWLER_SCHEDULER="false" \
  test-crawler
```

## GitHub Actions 调试

### 查看构建日志
1. 进入 GitHub 仓库的 Actions 页面
2. 点击失败的工作流运行
3. 展开 "Build and push Docker image" 步骤
4. 查看详细错误信息

### 常见 Actions 错误

#### 1. 登录失败
```
Error: Cannot perform an interactive login from a non TTY device
```

**解决方案**:
- 检查 GitHub Secrets 中的 `ALIYUN_REGISTRY_USERNAME` 和 `ALIYUN_REGISTRY_PASSWORD`
- 确认阿里云镜像仓库用户名和密码正确

#### 2. 推送失败
```
Error: failed to push: access denied
```

**解决方案**:
- 确认阿里云镜像仓库命名空间存在
- 检查用户权限是否足够

#### 3. 构建超时
```
Error: buildx failed with: ERROR: failed to solve: executor failed running
```

**解决方案**:
- ✅ 已优化：移除多平台构建，只构建 linux/amd64
- ✅ 已优化：使用构建缓存
- ✅ 已优化：减少构建上下文大小

## 性能优化

### 已实施的优化
1. **专用 package.json**: 只包含运行时必需的依赖
2. **构建缓存**: 使用 GitHub Actions 缓存
3. **单平台构建**: 只构建 linux/amd64 平台
4. **.dockerignore**: 排除不必要的文件
5. **分层构建**: 优化 Docker 层缓存

### 构建时间对比
- **优化前**: ~10-15 分钟
- **优化后**: ~3-5 分钟

## 监控构建状态

### 构建成功指标
- ✅ 测试构建通过
- ✅ 镜像推送成功
- ✅ 健康检查通过

### 自动化检查
```bash
# 检查最新镜像
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest

# 验证镜像
docker run --rm registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest node --version
```

## 回滚策略

### 使用稳定版本
```bash
# 回滚到稳定版本
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:stable

# 更新 docker-compose 文件
sed -i 's/:latest/:stable/g' docker-compose.crawler.yml

# 重新部署
docker-compose -f docker-compose.crawler.yml up -d
```

### 使用特定版本
```bash
# 查看可用版本
# 在阿里云控制台查看镜像标签

# 使用特定版本
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:v1.0.20241201-abc123

# 更新部署
./scripts/deploy-from-registry.sh v1.0.20241201-abc123
```

## 联系支持

如果遇到无法解决的问题：

1. **收集信息**:
   - GitHub Actions 构建日志
   - 本地测试结果
   - 错误截图

2. **检查清单**:
   - [ ] GitHub Secrets 配置正确
   - [ ] 阿里云镜像仓库权限正常
   - [ ] 网络连接正常
   - [ ] 依赖版本兼容

3. **临时解决方案**:
   - 使用本地构建: `./docker/deploy.sh`
   - 回滚到稳定版本
   - 检查服务状态: `./docker/manage.sh status`
