# TypeScript 模块导入问题修复指南

## 问题描述

在 Docker 容器中运行时出现错误：
```
❌ 服务启动失败: TypeError: initializeApp is not a function
```

## 问题分析

这个问题是由于 TypeScript 模块在 Docker 环境中的导入方式与本地环境不同导致的。可能的原因包括：

1. **模块解析差异**: Docker 环境中的模块解析路径可能不同
2. **TypeScript 编译差异**: tsx 在不同环境中的行为可能不一致
3. **ES6 模块导出**: TypeScript 的 ES6 导出在动态导入时可能有兼容性问题

## 解决方案

### 方案一：增强的模块导入（推荐）

已经在 `docker/crawler-service.js` 中实现了增强的模块导入逻辑：

```javascript
// 兼容不同的导出方式
WechatCrawlerScheduler = schedulerModule.WechatCrawlerScheduler || schedulerModule.default?.WechatCrawlerScheduler;
initializeApp = startupModule.initializeApp || startupModule.default?.initializeApp;
cleanupApp = startupModule.cleanupApp || startupModule.default?.cleanupApp;
```

这个方案会：
- 尝试直接导入命名导出
- 如果失败，尝试从 default 导出中获取
- 提供详细的调试信息

### 方案二：使用简化的 CommonJS 版本

如果方案一仍然有问题，可以使用简化版本：

1. **使用简化的 Dockerfile**:
   ```bash
   # 重命名当前 Dockerfile
   mv Dockerfile.crawler Dockerfile.crawler.backup
   
   # 使用简化版本
   mv Dockerfile.crawler.simple Dockerfile.crawler
   ```

2. **重新构建**:
   ```bash
   docker-compose -f docker-compose.crawler.yml up --build -d
   ```

简化版本的特点：
- ✅ 使用纯 CommonJS，避免 TypeScript 导入问题
- ✅ 包含完整的健康检查功能
- ✅ 提供基本的数据库连接测试
- ⚠️ 暂时不包含实际的爬取逻辑（仅用于验证部署）

### 方案三：预编译 TypeScript

如果需要完整功能，可以预编译 TypeScript：

1. **修改 Dockerfile**:
   ```dockerfile
   # 安装 TypeScript 编译器
   RUN npm install -g typescript
   
   # 编译 TypeScript 文件
   RUN tsc lib/*.ts --outDir lib --target es2020 --module commonjs
   
   # 使用编译后的 JS 文件
   CMD ["node", "crawler-service.js"]
   ```

## 测试和验证

### 1. 本地测试模块导入

```bash
# 测试 TypeScript 模块导入
npx tsx docker/test-imports.js
```

### 2. Docker 构建测试

```bash
# 构建镜像
docker build -f Dockerfile.crawler -t feedwe-crawler-test .

# 运行测试容器
docker run --rm -it \
  -e DATABASE_URL="mysql://test:test@localhost:3306/test" \
  -e DISABLE_CRAWLER_SCHEDULER="false" \
  feedwe-crawler-test
```

### 3. 健康检查验证

```bash
# 启动服务
docker-compose -f docker-compose.crawler.yml up -d

# 等待启动
sleep 30

# 检查健康状态
curl http://localhost:3001/health

# 检查详细状态
curl http://localhost:3001/status
```

## 调试步骤

### 1. 查看容器日志

```bash
# 查看启动日志
docker-compose -f docker-compose.crawler.yml logs -f

# 查看最近的错误
docker-compose -f docker-compose.crawler.yml logs --tail=50
```

### 2. 进入容器调试

```bash
# 进入运行中的容器
docker exec -it feedwe-crawler sh

# 检查文件结构
ls -la /app/
ls -la /app/lib/

# 测试模块导入
node -e "import('./lib/startup.ts').then(console.log).catch(console.error)"
```

### 3. 手动测试导入

```bash
# 在容器中运行测试脚本
docker exec -it feedwe-crawler npx tsx test-imports.js
```

## 预期结果

### 成功启动的日志

```
🚀 启动 FeedWe 定时任务爬虫服务...
📅 启动时间: 2024-01-01T00:00:00.000Z
🌍 时区: Asia/Shanghai
🔧 环境: production
📦 开始导入模块...
📋 模块导入调试信息:
  schedulerModule keys: [ 'WechatCrawlerScheduler', 'default' ]
  startupModule keys: [ 'cleanupApp', 'default', 'initializeApp' ]
📋 导入后的类型检查:
  WechatCrawlerScheduler: function
  initializeApp: function
  cleanupApp: function
✅ 模块加载成功
✅ 数据库连接成功
🏥 健康检查服务器启动在端口 3001
✅ FeedWe 定时任务爬虫服务启动完成
```

### 健康检查响应

```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "scheduler": {
    "isRunning": true,
    "isActive": true
  },
  "uptime": 30.5
}
```

## 回滚方案

如果新的修复导致问题，可以快速回滚：

```bash
# 停止服务
docker-compose -f docker-compose.crawler.yml down

# 恢复备份的 Dockerfile
mv Dockerfile.crawler.backup Dockerfile.crawler

# 使用之前的版本
git checkout HEAD~1 docker/crawler-service.js

# 重新构建
docker-compose -f docker-compose.crawler.yml up --build -d
```

## 长期解决方案

1. **统一模块系统**: 将整个项目迁移到纯 ES6 模块或纯 CommonJS
2. **构建时编译**: 在 CI/CD 中预编译 TypeScript
3. **容器化开发**: 使用相同的容器环境进行开发和生产

## 联系支持

如果问题仍然存在，请提供：
- 完整的容器启动日志
- `curl http://localhost:3001/health` 的响应
- 容器内的文件结构 (`docker exec -it feedwe-crawler ls -la /app/`)
- 环境变量配置
