version: '3.8'

services:
  feedwe-crawler:
    # 使用预构建的镜像（推荐生产环境）
    # image: registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest
    # 或者本地构建（开发环境）
    build:
      context: .
      dockerfile: Dockerfile.crawler
    container_name: feedwe-crawler
    restart: unless-stopped
    environment:
      # 数据库连接
      # DATABASE_URL: "${DATABASE_URL}"
      DATABASE_URL: "mysql://root:ZSmysql.1998@************:3306/feedwe"
      
      # 定时任务配置
      DISABLE_CRAWLER_SCHEDULER: "false"
      
      # 其他环境变量
      NODE_ENV: "production"
      TZ: "Asia/Shanghai"
    
    ports:
      - "3001:3001"
    
    volumes:
      - ./logs:/app/logs
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    networks:
      - feedwe-network

networks:
  feedwe-network:
    driver: bridge

volumes:
  logs:
    driver: local
