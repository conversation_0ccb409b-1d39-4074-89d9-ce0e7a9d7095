# Vercel部署指南

本文档提供了将FeedWe微信公众号订阅工具部署到Vercel的详细步骤。

## 前提条件

1. 一个GitHub、GitLab或Bitbucket账号，用于存储代码
2. 一个Vercel账号（可以使用GitHub账号登录）
3. 一个PostgreSQL数据库（可以使用Vercel Postgres、Supabase或PlanetScale）

## 步骤1：准备代码仓库

1. 将项目代码推送到Git仓库：

```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin <your-repository-url>
git push -u origin main
```

## 步骤2：设置数据库

### 选项1：Vercel Postgres（推荐）

1. 在Vercel控制台中，选择您的项目
2. 点击"Storage"选项卡
3. 选择"Connect Database"
4. 选择"Postgres"
5. 按照向导创建数据库
6. Vercel会自动将数据库连接字符串添加到环境变量

### 选项2：Supabase

1. 创建一个Supabase账号并登录
2. 创建一个新项目
3. 在项目设置中找到数据库连接字符串
4. 复制连接字符串，格式为：`postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres`

### 选项3：其他PostgreSQL提供商

1. 创建PostgreSQL数据库
2. 获取连接字符串
3. 确保数据库可以从Vercel的IP地址访问

## 步骤3：在Vercel上部署

1. 登录Vercel控制台：https://vercel.com/dashboard
2. 点击"New Project"
3. 导入您的Git仓库
4. 配置项目设置：
   - 框架预设：Next.js
   - 使用默认的构建设置（Vercel会自动使用`npm run build`）
   - 输出目录：`.next`

5. 添加环境变量：
   - `DATABASE_URL`: 您的PostgreSQL连接字符串
   - `JWT_SECRET`: 一个随机生成的密钥，用于JWT认证
   - `NEXTAUTH_URL`: 您的应用URL，例如 `https://your-app-name.vercel.app`
   - `NEXTAUTH_SECRET`: 一个随机生成的密钥

6. 点击"Deploy"按钮

## 步骤4：运行数据库迁移

首次部署后，您需要运行数据库迁移：

1. 在Vercel控制台中，选择您的项目
2. 点击"Deployments"选项卡
3. 找到最新的部署
4. 点击"Functions"选项卡
5. 找到"Logs"按钮，查看部署日志
6. 确认数据库迁移是否成功运行

如果迁移未自动运行，您可以手动运行：

```bash
# 本地运行迁移并推送到生产数据库
DATABASE_URL="your_production_db_url" npx prisma migrate deploy
```

## 步骤5：初始化数据

您可以使用以下方法之一初始化数据：

### 选项1：通过Vercel CLI

1. 安装Vercel CLI：`npm i -g vercel`
2. 登录：`vercel login`
3. 链接项目：`vercel link`
4. 运行种子脚本：`vercel env pull && npx prisma db seed`

### 选项2：通过数据库客户端

1. 使用数据库客户端（如pgAdmin、DBeaver等）连接到您的数据库
2. 手动插入初始数据

## 步骤6：验证部署

1. 访问您的Vercel应用URL
2. 确认应用正常运行
3. 测试关键功能：
   - 用户注册/登录
   - 订阅公众号
   - 查看文章
   - 导出Excel

## 故障排除

### 数据库连接问题

- 确认数据库连接字符串格式正确
- 检查数据库是否允许从Vercel的IP地址访问
- 验证数据库用户权限

### 部署失败

- 检查Vercel部署日志
- 确认所有环境变量已正确设置
- 验证package.json中的构建脚本

### Prisma错误

- 确保Prisma模型与数据库架构同步
- 检查是否正确运行了`prisma generate`
- 验证迁移是否成功应用

## 自动化部署

每次推送到主分支时，Vercel将自动重新部署您的应用。如果您更改了数据库架构，请确保在推送前运行`prisma migrate dev`并提交生成的迁移文件。

## 资源

- [Vercel文档](https://vercel.com/docs)
- [Next.js部署文档](https://nextjs.org/docs/deployment)
- [Prisma部署指南](https://www.prisma.io/docs/guides/deployment)
