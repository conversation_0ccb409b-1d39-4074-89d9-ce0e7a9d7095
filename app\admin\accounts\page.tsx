'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../components/AuthProvider';

// 工具函数：检查是否为微信图片URL
const isWechatImageUrl = (url: string): boolean => {
  if (!url) return false;
  const wechatDomains = ['mmbiz.qpic.cn', 'mmbiz.qlogo.cn', 'wx.qlogo.cn', 'thirdwx.qlogo.cn'];
  return wechatDomains.some(domain => url.includes(domain));
};

// 工具函数：获取代理后的图片URL（仅用于显示）
const getDisplayImageUrl = (url: string): string => {
  if (!url || !isWechatImageUrl(url)) return url;
  return `/api/proxy/wechat-image?url=${encodeURIComponent(url)}`;
};


interface WechatAccount {
  id: string;
  name: string;
  avatar: string;
  description?: string;
  openid: string;
  createdAt: string;
  updatedAt: string;
  subscriptionCount: number;
  articleCount: number;
  // 爬取相关字段
  enableCrawling?: boolean;
  lastCrawlTime?: string;
  crawlStatus?: string;
  crawlError?: string;
  fakeid?: string;
}

interface AccountStats {
  totalAccounts: number;
  activeAccounts: number;
  totalArticles: number;
  todayNewAccounts: number;
  popularAccounts: Array<{
    id: string;
    name: string;
    avatar: string;
    subscriptionCount: number;
  }>;
}

export default function AccountsManagement() {
  const { token } = useAuth();
  const [accounts, setAccounts] = useState<WechatAccount[]>([]);
  const [stats, setStats] = useState<AccountStats>({
    totalAccounts: 0,
    activeAccounts: 0,
    totalArticles: 0,
    todayNewAccounts: 0,
    popularAccounts: []
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searching, setSearching] = useState(false);
  const [adding, setAdding] = useState<string | null>(null);
  const [selectedAccount, setSelectedAccount] = useState<WechatAccount | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    description: '',
    avatar: ''
  });
  // 定时任务相关状态
  const [schedulerStatus, setSchedulerStatus] = useState<{
    isRunning: boolean;
    nextExecution?: string;
  }>({ isRunning: false });
  const [showCrawlingModal, setShowCrawlingModal] = useState(false);
  const [crawlingAccount, setCrawlingAccount] = useState<WechatAccount | null>(null);

  useEffect(() => {
    fetchAccounts();
    fetchStats();
    fetchSchedulerStatus();
  }, [currentPage, searchTerm]);

  const fetchAccounts = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm
      });

      const response = await fetch(`/api/admin/accounts?${params}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setAccounts(data.accounts);
        setTotalPages(data.totalPages);
      }
    } catch (error) {
      console.error('获取公众号列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/accounts/stats', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('获取公众号统计失败:', error);
    }
  };

  // 获取定时任务状态
  const fetchSchedulerStatus = async () => {
    try {
      const response = await fetch('/api/admin/crawler/scheduler', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setSchedulerStatus({
          isRunning: data.isRunning,
          nextExecution: data.nextExecution
        });
      }
    } catch (error) {
      console.error('获取定时任务状态失败:', error);
    }
  };

  // 启动定时任务
  const startScheduler = async () => {
    try {
      const response = await fetch('/api/admin/crawler/scheduler', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        alert('定时任务已启动');
        fetchSchedulerStatus();
      } else {
        const data = await response.json();
        alert(data.error || '启动失败');
      }
    } catch (error) {
      console.error('启动定时任务失败:', error);
      alert('启动失败');
    }
  };

  // 停止定时任务
  const stopScheduler = async () => {
    try {
      const response = await fetch('/api/admin/crawler/scheduler', {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        alert('定时任务已停止');
        fetchSchedulerStatus();
      } else {
        const data = await response.json();
        alert(data.error || '停止失败');
      }
    } catch (error) {
      console.error('停止定时任务失败:', error);
      alert('停止失败');
    }
  };

  // 打开爬取设置模态框
  const handleCrawlingSetting = (account: WechatAccount) => {
    setCrawlingAccount(account);
    setShowCrawlingModal(true);
  };

  // 切换爬取设置
  const toggleCrawling = async (accountId: string, enableCrawling: boolean) => {
    try {
      const response = await fetch(`/api/admin/accounts/${accountId}/crawling`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enableCrawling })
      });

      if (response.ok) {
        const data = await response.json();
        alert(data.message);
        fetchAccounts(); // 刷新列表
        setShowCrawlingModal(false);
      } else {
        const data = await response.json();
        alert(data.error || '设置失败');
      }
    } catch (error) {
      console.error('设置爬取状态失败:', error);
      alert('设置失败');
    }
  };

  const handleEdit = (account: WechatAccount) => {
    setSelectedAccount(account);
    setEditForm({
      name: account.name,
      description: account.description || '',
      avatar: account.avatar
    });
    setShowEditModal(true);
  };

  const handleSaveEdit = async () => {
    if (!selectedAccount) return;

    try {
      const response = await fetch(`/api/admin/accounts/${selectedAccount.id}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editForm)
      });

      if (response.ok) {
        setShowEditModal(false);
        fetchAccounts();
        fetchStats();
      }
    } catch (error) {
      console.error('更新公众号失败:', error);
    }
  };

  const handleDelete = async (accountId: string, accountName: string) => {
    if (!confirm(`确定要删除公众号"${accountName}"吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/accounts/${accountId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        fetchAccounts();
        fetchStats();
      } else {
        const data = await response.json();
        alert(data.error || '删除失败');
      }
    } catch (error) {
      console.error('删除公众号失败:', error);
    }
  };

  const searchWechatAccounts = async () => {
    if (!searchKeyword.trim()) {
      alert('请输入搜索关键词');
      return;
    }

    setSearching(true);
    try {
      const params = new URLSearchParams({
        keyword: searchKeyword,
        begin: '0',
        size: '10'
      });

      const response = await fetch(`/api/admin/accounts/search?${params}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
      } else {
        const data = await response.json();
        alert(data.error || '搜索失败');
      }
    } catch (error) {
      console.error('搜索公众号失败:', error);
      alert('搜索失败');
    } finally {
      setSearching(false);
    }
  };

  const addWechatAccount = async (account: any) => {
    setAdding(account.fakeid);
    try {
      const response = await fetch('/api/admin/accounts/search', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fakeid: account.fakeid,
          nickname: account.nickname,
          alias: account.alias,
          avatar: account.avatar,
          serviceType: account.serviceType,
          signature: account.signature,
          verifyStatus: account.verifyStatus
        })
      });

      if (response.ok) {
        alert('公众号添加成功！');
        // 更新搜索结果中的状态
        setSearchResults(prev =>
          prev.map(item =>
            item.fakeid === account.fakeid
              ? { ...item, isAdded: true }
              : item
          )
        );
        // 刷新公众号列表
        fetchAccounts();
      } else {
        const data = await response.json();
        alert(data.error || '添加失败');
      }
    } catch (error) {
      console.error('添加公众号失败:', error);
      alert('添加失败');
    } finally {
      setAdding(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">公众号管理</h1>
          <p className="text-gray-600">管理系统中的微信公众号</p>
        </div>
        <button
          onClick={() => setShowSearchModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          搜索添加公众号
        </button>
      </div>

      {/* 定时任务状态 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-gray-900">文章爬取定时任务</h3>
            <p className="text-sm text-gray-600 mt-1">
              状态: <span className={`font-medium ${schedulerStatus.isRunning ? 'text-green-600' : 'text-gray-600'}`}>
                {schedulerStatus.isRunning ? '运行中' : '已停止'}
              </span>
              {schedulerStatus.nextExecution && (
                <span className="ml-4 text-gray-500">
                  下次执行: {new Date(schedulerStatus.nextExecution).toLocaleTimeString()}
                </span>
              )}
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={fetchSchedulerStatus}
              className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              🔄 刷新状态
            </button>
            {schedulerStatus.isRunning ? (
              <button
                onClick={stopScheduler}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                停止任务
              </button>
            ) : (
              <button
                onClick={startScheduler}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                启动任务
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总公众号数</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalAccounts}</p>
            </div>
            <div className="text-3xl">📢</div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">活跃公众号</p>
              <p className="text-2xl font-bold text-green-600">{stats.activeAccounts}</p>
            </div>
            <div className="text-3xl">✅</div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">总文章数</p>
              <p className="text-2xl font-bold text-blue-600">{stats.totalArticles}</p>
            </div>
            <div className="text-3xl">📄</div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">今日新增</p>
              <p className="text-2xl font-bold text-purple-600">{stats.todayNewAccounts}</p>
            </div>
            <div className="text-3xl">🆕</div>
          </div>
        </div>
      </div>

      {/* 热门公众号 */}
      {stats.popularAccounts.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">热门公众号</h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {stats.popularAccounts.map((account) => (
              <div key={account.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <img
                  src={account.avatar}
                  alt={account.name}
                  className="w-10 h-10 rounded-full"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {account.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {account.subscriptionCount} 订阅
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 搜索 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索公众号名称或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* 公众号列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {loading ? (
          <div className="p-8 text-center text-gray-500">加载中...</div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      公众号信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      订阅数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      文章数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      爬取状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {accounts.map((account) => (
                    <tr key={account.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <img
                            src={account.avatar}
                            alt={account.name}
                            className="w-12 h-12 rounded-full mr-4"
                          />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {account.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {account.description?.slice(0, 50)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(account.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {account.subscriptionCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {account.articleCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              account.enableCrawling
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {account.enableCrawling ? '已启用' : '未启用'}
                            </span>
                            {account.crawlStatus && account.crawlStatus !== 'idle' && (
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                account.crawlStatus === 'running'
                                  ? 'bg-blue-100 text-blue-800'
                                  : account.crawlStatus === 'error'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {account.crawlStatus === 'running' ? '运行中' :
                                 account.crawlStatus === 'error' ? '错误' : account.crawlStatus}
                              </span>
                            )}
                          </div>
                          {account.lastCrawlTime && (
                            <div className="text-xs text-gray-400">
                              最后爬取: {new Date(account.lastCrawlTime).toLocaleString()}
                            </div>
                          )}
                          {account.crawlError && (
                            <div className="text-xs text-red-500 truncate max-w-32" title={account.crawlError}>
                              {account.crawlError}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => handleEdit(account)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          编辑
                        </button>
                        <button
                          onClick={() => handleCrawlingSetting(account)}
                          className="text-green-600 hover:text-green-900"
                          title="爬取设置"
                        >
                          爬取
                        </button>
                        <button
                          onClick={() => handleDelete(account.id, account.name)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-700">
                    第 {currentPage} 页，共 {totalPages} 页
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* 编辑模态框 */}
      {showEditModal && selectedAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">编辑公众号</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  公众号名称
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  头像URL
                </label>
                <input
                  type="text"
                  value={editForm.avatar}
                  onChange={(e) => setEditForm({ ...editForm, avatar: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300"
              >
                取消
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 搜索公众号模态框 */}
      {showSearchModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">搜索微信公众号</h3>
              <button
                onClick={() => {
                  setShowSearchModal(false);
                  setSearchKeyword('');
                  setSearchResults([]);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            {/* 搜索框 */}
            <div className="flex gap-3 mb-6">
              <input
                type="text"
                placeholder="输入公众号名称或关键词..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && searchWechatAccounts()}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={searchWechatAccounts}
                disabled={searching}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {searching ? '搜索中...' : '搜索'}
              </button>
            </div>

            {/* 搜索结果 */}
            {searchResults.length > 0 && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">搜索结果 ({searchResults.length})</h4>
                <div className="grid gap-4">
                  {searchResults.map((account) => (
                    <div key={account.fakeid} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <img
                            src={getDisplayImageUrl(account.avatar)}
                            alt={account.nickname}
                            className="w-16 h-16 rounded-full"
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h5 className="font-medium text-gray-900">{account.nickname}</h5>
                              {account.verifyStatus === 1 && (
                                <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                  已认证
                                </span>
                              )}
                            </div>
                            {account.alias && (
                              <p className="text-sm text-gray-500 mt-1">微信号: {account.alias}</p>
                            )}
                            <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                              {account.signature || '暂无简介'}
                            </p>
                            <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                              <span>FakeID: {account.fakeid}</span>
                              <span>服务类型: {account.serviceType === 1 ? '订阅号' : account.serviceType === 2 ? '服务号' : '其他'}</span>
                            </div>
                          </div>
                        </div>
                        <div className="ml-4">
                          {account.isAdded ? (
                            <span className="inline-flex items-center px-3 py-1 text-sm font-medium bg-green-100 text-green-800 rounded-full">
                              已添加
                            </span>
                          ) : (
                            <button
                              onClick={() => addWechatAccount(account)}
                              disabled={adding === account.fakeid}
                              className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                              {adding === account.fakeid ? '添加中...' : '添加'}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 空状态 */}
            {!searching && searchKeyword && searchResults.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-4">🔍</div>
                <p>未找到相关公众号</p>
                <p className="text-sm mt-2">请尝试其他关键词</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 爬取设置模态框 */}
      {showCrawlingModal && crawlingAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">爬取设置</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <img
                  src={getDisplayImageUrl(crawlingAccount.avatar)}
                  alt={crawlingAccount.name}
                  className="w-12 h-12 rounded-full"
                />
                <div>
                  <h4 className="font-medium text-gray-900">{crawlingAccount.name}</h4>
                  <p className="text-sm text-gray-500">
                    {crawlingAccount.fakeid ? `FakeID: ${crawlingAccount.fakeid}` : '缺少FakeID'}
                  </p>
                </div>
              </div>

              {!crawlingAccount.fakeid && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-center">
                    <div className="text-yellow-400 mr-2">⚠️</div>
                    <div className="text-sm text-yellow-800">
                      该公众号缺少FakeID，无法启用定时爬取功能
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">启用定时爬取</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={crawlingAccount.enableCrawling || false}
                      onChange={(e) => toggleCrawling(crawlingAccount.id, e.target.checked)}
                      disabled={!crawlingAccount.fakeid}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:opacity-50"></div>
                  </label>
                </div>

                {crawlingAccount.lastCrawlTime && (
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">最后爬取时间:</span>
                    <br />
                    {new Date(crawlingAccount.lastCrawlTime).toLocaleString()}
                  </div>
                )}

                {crawlingAccount.crawlError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="text-sm text-red-800">
                      <span className="font-medium">最后错误:</span>
                      <br />
                      {crawlingAccount.crawlError}
                    </div>
                  </div>
                )}
              </div>

              <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
                <p><strong>说明:</strong></p>
                <ul className="mt-1 space-y-1">
                  <li>• 定时任务每2分钟执行一次</li>
                  <li>• 只有启用爬取的公众号才会被处理</li>
                  <li>• 需要有效的微信登录凭证</li>
                </ul>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCrawlingModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
