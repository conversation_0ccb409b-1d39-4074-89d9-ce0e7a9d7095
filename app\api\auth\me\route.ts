import { NextRequest, NextResponse } from 'next/server';
import { getUserFromRequest } from '../../../lib/auth';
import { prisma } from '../../../../lib/prisma';

export async function GET(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);

    if (!user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取用户详细信息，包括邀请统计
    const userDetails = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        role: true,
        inviteCode: true,
        createdAt: true,
        _count: {
          select: {
            invitees: true,
            subscriptions: true,
          }
        },
        inviteRewards: {
          select: {
            amount: true,
            status: true,
          }
        }
      }
    });

    if (!userDetails) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    // 计算邀请奖励统计
    const totalRewards = userDetails.inviteRewards.reduce((sum: number, reward: any) => {
      return sum + Number(reward.amount);
    }, 0);

    const paidRewards = userDetails.inviteRewards
      .filter((reward: any) => reward.status === 'paid')
      .reduce((sum: number, reward: any) => sum + Number(reward.amount), 0);

    const pendingRewards = userDetails.inviteRewards
      .filter((reward: any) => reward.status === 'pending')
      .reduce((sum: number, reward: any) => sum + Number(reward.amount), 0);

    return NextResponse.json({
      success: true,
      user: {
        ...userDetails,
        stats: {
          inviteCount: userDetails._count.invitees,
          subscriptionCount: userDetails._count.subscriptions,
          totalRewards,
          paidRewards,
          pendingRewards,
        }
      }
    });

  } catch (error) {
    console.error('获取用户信息失败:', error);
    return NextResponse.json(
      { error: '获取用户信息失败' },
      { status: 500 }
    );
  }
}
