import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.SUBSCRIPTION_MANAGEMENT)(user);

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 并行查询订阅统计数据
    const [
      total,
      active,
      inactive,
      todayNew
    ] = await Promise.all([
      // 总订阅数
      prisma.subscription.count(),
      
      // 活跃订阅数
      prisma.subscription.count({
        where: { isActive: true }
      }),
      
      // 非活跃订阅数
      prisma.subscription.count({
        where: { isActive: false }
      }),
      
      // 今日新增订阅
      prisma.subscription.count({
        where: {
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      })
    ]);

    return NextResponse.json({
      total,
      active,
      inactive,
      todayNew
    });
  } catch (error) {
    console.error('获取订阅统计失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取订阅统计失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
