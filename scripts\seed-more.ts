import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 更多公众号数据
const moreAccounts = [
  {
    id: 'xinhua_001',
    name: '新华社',
    avatar: 'https://via.placeholder.com/100x100/059669/FFFFFF?text=新华社',
    openid: 'xinhua_001',
    description: '新华社官方微信公众号，国内外重要新闻'
  },
  {
    id: 'cctv_001',
    name: '央视新闻',
    avatar: 'https://via.placeholder.com/100x100/DC2626/FFFFFF?text=央视',
    openid: 'cctv_001',
    description: '央视新闻官方微信公众号，24小时新闻资讯'
  },
  {
    id: 'jjrb_001',
    name: '经济日报',
    avatar: 'https://via.placeholder.com/100x100/EA580C/FFFFFF?text=经济',
    openid: 'jjrb_001',
    description: '经济日报官方微信，财经新闻和分析'
  },
  {
    id: 'tech_001',
    name: '36氪',
    avatar: 'https://via.placeholder.com/100x100/8B5CF6/FFFFFF?text=36氪',
    openid: 'tech_001',
    description: '36氪官方微信，创投资讯和科技新闻'
  },
  {
    id: 'health_001',
    name: '丁香医生',
    avatar: 'https://via.placeholder.com/100x100/10B981/FFFFFF?text=丁香',
    openid: 'health_001',
    description: '丁香医生官方微信，专业健康科普'
  }
];

// 更多文章模板
const articleTemplates = [
  {
    title: '人工智能技术的最新突破与应用前景',
    summary: '近期人工智能领域取得了重大突破，从大语言模型到计算机视觉，各个方向都有显著进展。当前人工智能技术正处于快速发展阶段，特别是在深度学习、自然语言处理等领域取得了突破性进展。',
    url: 'https://mp.weixin.qq.com/s/ai-breakthrough-applications',
    coverImage: 'https://via.placeholder.com/600x300/4F46E5/FFFFFF?text=AI技术',
    author: '科技前沿',
    tags: ['人工智能', 'AI', '技术突破', '应用前景']
  },
  {
    title: '健康生活方式的科学指南',
    summary: '科学研究表明，良好的生活习惯对身体健康有着重要影响。现代生活节奏快，工作压力大，很多人忽视了健康生活方式的重要性。本文为您介绍实用的健康生活建议，包括科学饮食、运动健身和睡眠管理。',
    url: 'https://mp.weixin.qq.com/s/healthy-lifestyle-guide',
    coverImage: 'https://via.placeholder.com/600x300/10B981/FFFFFF?text=健康生活',
    author: '健康专家',
    tags: ['健康', '生活方式', '饮食', '运动', '睡眠']
  },
  {
    title: '数字化转型助力企业高质量发展',
    summary: '在数字经济时代，企业数字化转型已成为提升竞争力的关键路径。随着数字技术的快速发展，传统企业面临着前所未有的挑战和机遇，需要制定清晰的数字化战略。',
    url: 'https://mp.weixin.qq.com/s/digital-transformation-enterprise',
    coverImage: 'https://via.placeholder.com/600x300/7C3AED/FFFFFF?text=数字化转型',
    author: '数字化专家',
    tags: ['数字化转型', '企业发展', '数字经济', '技术创新']
  },
  {
    title: '新能源汽车产业发展现状与趋势',
    summary: '新能源汽车产业正迎来快速发展期，技术不断进步，市场规模持续扩大。我国新能源汽车产业已进入规模化快速发展阶段，产销量连续多年位居世界第一。',
    url: 'https://mp.weixin.qq.com/s/new-energy-vehicle-industry',
    coverImage: 'https://via.placeholder.com/600x300/059669/FFFFFF?text=新能源汽车',
    author: '汽车行业分析师',
    tags: ['新能源汽车', '产业发展', '电池技术', '市场趋势']
  },
  {
    title: '教育改革创新的实践与思考',
    summary: '教育是国之大计，深化教育改革创新对于培养高素质人才具有重要意义。面对新时代的要求，教育改革势在必行，需要培养更多创新型人才。',
    url: 'https://mp.weixin.qq.com/s/education-reform-innovation',
    coverImage: 'https://via.placeholder.com/600x300/F59E0B/FFFFFF?text=教育改革',
    author: '教育专家',
    tags: ['教育改革', '创新实践', '人才培养', '教学方法']
  }
];

async function main() {
  console.log('开始生成更多测试数据...');

  // 创建更多公众号
  for (const accountData of moreAccounts) {
    const account = await prisma.wechatAccount.upsert({
      where: { openid: accountData.openid },
      update: accountData,
      create: accountData
    });

    console.log(`创建公众号: ${account.name}`);

    // 为每个公众号创建多篇文章
    for (let i = 0; i < articleTemplates.length; i++) {
      const template = articleTemplates[i];
      const publishDate = new Date();
      publishDate.setDate(publishDate.getDate() - Math.floor(Math.random() * 30)); // 随机过去30天内的日期

      await prisma.article.upsert({
        where: { id: `${account.id}_article_${i + 1}` },
        update: {},
        create: {
          id: `${account.id}_article_${i + 1}`,
          title: template.title,
          summary: template.summary,
          url: template.url,
          publishDate: publishDate,
          readCount: Math.floor(Math.random() * 50000) + 1000,
          likeCount: Math.floor(Math.random() * 2000) + 50,
          coverImage: template.coverImage,
          author: template.author,
          tags: Array.isArray(template.tags) ? template.tags.join(',') : '',
          wechatAccountId: account.id
        }
      });
    }

    // 为现有公众号也添加更多文章
    const existingAccounts = await prisma.wechatAccount.findMany({
      where: {
        id: {
          in: ['rmrb_001', 'kjrb_001']
        }
      }
    });

    for (const existingAccount of existingAccounts) {
      for (let i = 3; i < articleTemplates.length; i++) { // 从第4篇开始添加
        const template = articleTemplates[i];
        const publishDate = new Date();
        publishDate.setDate(publishDate.getDate() - Math.floor(Math.random() * 60)); // 随机过去60天内的日期

        await prisma.article.upsert({
          where: { id: `${existingAccount.id}_article_${i + 1}` },
          update: {},
          create: {
            id: `${existingAccount.id}_article_${i + 1}`,
            title: `${existingAccount.name} - ${template.title}`,
            summary: template.summary,
            url: `${template.url}?from=${existingAccount.id}`,
            publishDate: publishDate,
            readCount: Math.floor(Math.random() * 30000) + 500,
            likeCount: Math.floor(Math.random() * 1500) + 30,
            coverImage: template.coverImage,
            author: template.author || existingAccount.name,
            tags: Array.isArray(template.tags) ? template.tags.join(',') : '',
            wechatAccountId: existingAccount.id
          }
        });
      }
    }
  }

  // 为演示用户创建一些订阅
  const demoUserId = 'demo-user-id';
  const accountsToSubscribe = ['rmrb_001', 'kjrb_001', 'xinhua_001', 'tech_001'];

  for (const accountId of accountsToSubscribe) {
    const account = await prisma.wechatAccount.findFirst({
      where: { id: accountId }
    });

    if (account) {
      await prisma.subscription.upsert({
        where: {
          userId_wechatAccountId: {
            userId: demoUserId,
            wechatAccountId: account.id
          }
        },
        update: {},
        create: {
          userId: demoUserId,
          wechatAccountId: account.id
        }
      });
      console.log(`为用户创建订阅: ${account.name}`);
    }
  }

  console.log('更多测试数据生成完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
