#!/bin/bash

# 模块导入问题修复方案选择器

set -e

echo "🔧 FeedWe Docker 模块导入问题修复"
echo "=================================="
echo ""
echo "检测到 'initializeApp is not a function' 错误"
echo "请选择修复方案："
echo ""
echo "1. 增强的 TypeScript 模块导入（推荐）"
echo "   - 兼容多种导出方式"
echo "   - 包含详细调试信息"
echo "   - 保持完整功能"
echo ""
echo "2. 简化的 CommonJS 版本"
echo "   - 避免 TypeScript 导入问题"
echo "   - 快速验证 Docker 部署"
echo "   - 基础健康检查功能"
echo ""
echo "3. 查看详细的修复指南"
echo ""

read -p "请选择方案 (1/2/3): " choice

case $choice in
    1)
        echo "🔄 应用增强的 TypeScript 模块导入方案..."
        
        # 确保使用增强版本的 crawler-service.js
        if grep -q "兼容不同的导出方式" docker/crawler-service.js; then
            echo "✅ 增强版本已经应用"
        else
            echo "❌ 需要手动应用增强版本"
            echo "请确保 docker/crawler-service.js 包含增强的导入逻辑"
            exit 1
        fi
        
        # 使用标准 Dockerfile
        if [ -f "Dockerfile.crawler.backup" ]; then
            echo "🔄 恢复标准 Dockerfile..."
            mv Dockerfile.crawler.backup Dockerfile.crawler
        fi
        
        echo "🚀 重新构建并启动服务..."
        docker-compose -f docker-compose.crawler.yml down || true
        docker-compose -f docker-compose.crawler.yml up --build -d
        
        echo "⏳ 等待服务启动（30秒）..."
        sleep 30
        
        echo "🏥 检查健康状态..."
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            echo "✅ 方案1 修复成功！"
            curl -s http://localhost:3001/status | python3 -m json.tool 2>/dev/null || curl -s http://localhost:3001/status
        else
            echo "❌ 方案1 修复失败，建议尝试方案2"
            echo "📋 查看日志："
            docker-compose -f docker-compose.crawler.yml logs --tail=20
        fi
        ;;
        
    2)
        echo "🔄 应用简化的 CommonJS 版本..."
        
        # 备份当前 Dockerfile
        if [ -f "Dockerfile.crawler" ] && [ ! -f "Dockerfile.crawler.backup" ]; then
            cp Dockerfile.crawler Dockerfile.crawler.backup
            echo "📦 已备份当前 Dockerfile"
        fi
        
        # 使用简化版本
        if [ -f "Dockerfile.crawler.simple" ]; then
            cp Dockerfile.crawler.simple Dockerfile.crawler
            echo "✅ 已切换到简化版本"
        else
            echo "❌ 简化版本文件不存在"
            exit 1
        fi
        
        echo "🚀 重新构建并启动服务..."
        docker-compose -f docker-compose.crawler.yml down || true
        docker-compose -f docker-compose.crawler.yml up --build -d
        
        echo "⏳ 等待服务启动（30秒）..."
        sleep 30
        
        echo "🏥 检查健康状态..."
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            echo "✅ 方案2 修复成功！"
            echo "💡 注意：这是简化版本，主要用于验证部署"
            curl -s http://localhost:3001/status | python3 -m json.tool 2>/dev/null || curl -s http://localhost:3001/status
        else
            echo "❌ 方案2 也失败了，请查看详细指南"
            echo "📋 查看日志："
            docker-compose -f docker-compose.crawler.yml logs --tail=20
        fi
        ;;
        
    3)
        echo "📚 打开详细修复指南..."
        if command -v code &> /dev/null; then
            code docs/module-import-fix.md
        elif command -v cat &> /dev/null; then
            cat docs/module-import-fix.md
        else
            echo "请查看文件: docs/module-import-fix.md"
        fi
        ;;
        
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔧 有用的命令："
echo "  查看日志: docker-compose -f docker-compose.crawler.yml logs -f"
echo "  重启服务: docker-compose -f docker-compose.crawler.yml restart"
echo "  停止服务: docker-compose -f docker-compose.crawler.yml down"
echo "  健康检查: curl http://localhost:3001/health"
