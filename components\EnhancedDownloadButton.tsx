'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2, Image, Wifi, WifiOff } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/app/components/AuthProvider';

// 工具函数：下载HTML文件到本地
const downloadHtmlFile = (htmlContent: string, fileName: string, mode: string) => {
  try {
    // 创建Blob对象
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 清理文件名，移除特殊字符
    const cleanFileName = fileName
      .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
      .replace(/\s+/g, '_') // 空格替换为下划线
      .substring(0, 80); // 限制长度

    const modePrefix = mode === 'offline' ? '离线版_' : '在线版_';
    link.download = `${modePrefix}${cleanFileName}_${new Date().getTime()}.html`;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载文件失败:', error);
    throw new Error('下载文件失败');
  }
};

interface EnhancedDownloadButtonProps {
  articleId: string;
  articleTitle: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  showModeSelector?: boolean; // 是否显示模式选择器
}

export function EnhancedDownloadButton({
  articleId,
  articleTitle,
  className,
  variant = 'outline',
  size = 'sm',
  showModeSelector = true
}: EnhancedDownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const { token } = useAuth();

  const handleDownload = async (mode: 'online' | 'offline') => {
    if (isDownloading) return;

    if (!token) {
      toast.error('请先登录');
      return;
    }

    setIsDownloading(true);
    setShowOptions(false);

    try {
      const endpoint = mode === 'offline' ? '/api/articles/download-offline' : '/api/articles/download';
      const requestBody = mode === 'offline' 
        ? { articleId, includeImages: true, options: { withCredentials: true, timeout: 60 } }
        : { articleId, options: { withCredentials: true, timeout: 30 } };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '下载失败');
      }

      if (data.success && data.article && data.article.html) {
        // 直接下载到本地
        downloadHtmlFile(data.article.html, data.article.title, mode);
        
        const modeText = mode === 'offline' ? '离线版本（包含图片）' : '在线版本';
        toast.success(`文章${modeText}已下载到本地`);
      } else {
        toast.success('下载任务已创建，请稍后查看下载状态');
      }
    } catch (error) {
      console.error('下载失败:', error);
      toast.error(error instanceof Error ? error.message : '下载失败');
    } finally {
      setIsDownloading(false);
    }
  };

  if (!showModeSelector) {
    // 简单模式：直接下载在线版本
    return (
      <Button
        variant={variant}
        size={size}
        onClick={() => handleDownload('online')}
        disabled={isDownloading}
        className={className}
        title={`下载文章: ${articleTitle}`}
      >
        {isDownloading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Download className="h-4 w-4" />
        )}
        {size !== 'sm' && (
          <span className="ml-2">
            {isDownloading ? '下载中...' : '下载'}
          </span>
        )}
      </Button>
    );
  }

  // 增强模式：显示下载选项
  return (
    <div className="relative inline-block">
      <Button
        variant={variant}
        size={size}
        onClick={() => setShowOptions(!showOptions)}
        disabled={isDownloading}
        className={className}
        title={`下载选项: ${articleTitle}`}
      >
        {isDownloading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Download className="h-4 w-4" />
        )}
        {size !== 'sm' && (
          <span className="ml-2">
            {isDownloading ? '下载中...' : '下载'}
          </span>
        )}
      </Button>

      {showOptions && !isDownloading && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-48">
          <div className="py-1">
            <button
              onClick={() => handleDownload('online')}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Wifi className="h-4 w-4 mr-3 text-blue-500" />
              <div className="text-left">
                <div className="font-medium">在线版本</div>
                <div className="text-xs text-gray-500">需要网络查看图片，文件较小</div>
              </div>
            </button>
            <button
              onClick={() => handleDownload('offline')}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <WifiOff className="h-4 w-4 mr-3 text-green-500" />
              <div className="text-left">
                <div className="font-medium">离线版本</div>
                <div className="text-xs text-gray-500">包含图片，可离线查看，文件较大</div>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* 点击外部关闭选项 */}
      {showOptions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowOptions(false)}
        />
      )}
    </div>
  );
}
