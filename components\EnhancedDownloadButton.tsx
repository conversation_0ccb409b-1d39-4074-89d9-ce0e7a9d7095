'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/app/components/AuthProvider';

// 工具函数：下载HTML文件到本地
const downloadHtmlFile = (htmlContent: string, fileName: string) => {
  try {
    // 创建Blob对象
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 清理文件名，移除特殊字符
    const cleanFileName = fileName
      .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
      .replace(/\s+/g, '_') // 空格替换为下划线
      .substring(0, 100); // 限制长度

    link.download = `${cleanFileName}_${new Date().getTime()}.html`;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载文件失败:', error);
    throw new Error('下载文件失败');
  }
};

interface EnhancedDownloadButtonProps {
  articleId: string;
  articleTitle: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
}

export function EnhancedDownloadButton({
  articleId,
  articleTitle,
  className,
  variant = 'outline',
  size = 'sm'
}: EnhancedDownloadButtonProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const { token } = useAuth();

  const handleDownload = async () => {
    if (isDownloading) return;

    if (!token) {
      toast.error('请先登录');
      return;
    }

    setIsDownloading(true);

    try {
      const response = await fetch('/api/articles/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          articleId,
          options: {
            withCredentials: true,
            timeout: 30
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '下载失败');
      }

      if (data.success && data.article && data.article.html) {
        // 直接下载到本地
        downloadHtmlFile(data.article.html, data.article.title);
        toast.success('文章已下载到本地');
      } else {
        toast.success('下载任务已创建，请稍后查看下载状态');
      }
    } catch (error) {
      console.error('下载失败:', error);
      toast.error(error instanceof Error ? error.message : '下载失败');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleDownload}
      disabled={isDownloading}
      className={className}
      title={`下载文章: ${articleTitle}`}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
      {size !== 'sm' && (
        <span className="ml-2">
          {isDownloading ? '下载中...' : '下载'}
        </span>
      )}
    </Button>
  );
}
