import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface DownloadOptions {
  withCredentials?: boolean;
  timeout?: number; // 超时时间（秒）
  maxRetries?: number; // 最大重试次数
}

export interface DownloadResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  errorMessage?: string;
  proxyServerId?: string;
}

export class ArticleDownloader {
  private defaultOptions: DownloadOptions = {
    withCredentials: true,
    timeout: 30,
    maxRetries: 3
  };

  /**
   * 获取可用的代理服务器列表
   */
  private async getAvailableProxies(): Promise<any[]> {
    return await prisma.proxyServer.findMany({
      where: { isActive: true },
      orderBy: { priority: 'asc' }
    });
  }

  /**
   * 使用代理下载资源
   */
  private async downloadWithProxy(
    url: string, 
    proxy: string | undefined, 
    options: DownloadOptions = {}
  ): Promise<Response> {
    const { withCredentials = true, timeout = 30 } = { ...this.defaultOptions, ...options };
    
    const headers: Record<string, string> = {};
    
    if (withCredentials) {
      try {
        // 从环境变量或配置中获取微信凭据
        const credentials = await this.getWechatCredentials();
        if (credentials) {
          headers.cookie = `pass_ticket=${credentials.pass_ticket};wap_sid2=${credentials.wap_sid2}`;
        }
      } catch (e) {
        console.warn('获取微信凭据失败:', e);
      }
    }

    let targetURL = proxy 
      ? `${proxy}?url=${encodeURIComponent(url)}&headers=${encodeURIComponent(JSON.stringify(headers))}`
      : url;
    
    // 强制使用HTTPS
    targetURL = targetURL.replace(/^http:\/\//, 'https://');

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout * 1000);

    try {
      const response = await fetch(targetURL, {
        signal: controller.signal,
        headers: proxy ? {} : headers
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 获取微信凭据
   */
  private async getWechatCredentials(): Promise<any> {
    // 从系统配置中获取微信凭据
    const configs = await prisma.systemConfig.findMany({
      where: {
        name: {
          in: ['wechat_token', 'wechat_cookie', 'wechat_data_ticket']
        }
      }
    });

    const credentials: any = {};
    configs.forEach(config => {
      switch (config.name) {
        case 'wechat_token':
          credentials.token = config.value;
          break;
        case 'wechat_cookie':
          credentials.cookie = config.value;
          break;
        case 'wechat_data_ticket':
          credentials.dataTicket = config.value;
          break;
      }
    });

    // 解析cookie中的pass_ticket和wap_sid2
    if (credentials.cookie) {
      const passTicketMatch = credentials.cookie.match(/pass_ticket=([^;]+)/);
      const wapSid2Match = credentials.cookie.match(/wap_sid2=([^;]+)/);
      
      if (passTicketMatch) credentials.pass_ticket = passTicketMatch[1];
      if (wapSid2Match) credentials.wap_sid2 = wapSid2Match[1];
    }

    return credentials;
  }

  /**
   * 下载单篇文章的HTML
   */
  public async downloadArticleHTML(
    articleUrl: string,
    title?: string,
    options: DownloadOptions = {}
  ): Promise<{ html: string; size: number; cleanedHtml?: string }> {
    const proxies = await this.getAvailableProxies();
    const { maxRetries = 3 } = { ...this.defaultOptions, ...options };
    
    let lastError: Error | null = null;
    
    // 尝试使用每个代理服务器
    for (const proxy of proxies) {
      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          console.log(`🔄 尝试下载文章 "${title}" (代理: ${proxy.name}, 尝试: ${attempt + 1}/${maxRetries})`);
          
          const response = await this.downloadWithProxy(articleUrl, proxy.url, options);
          const html = await response.text();
          
          // 验证HTML是否完整
          if (!this.validateArticleHTML(html, title)) {
            throw new Error('文章HTML不完整或已被删除');
          }
          
          const size = new Blob([html]).size;
          console.log(`✅ 文章 "${title}" 下载成功 (大小: ${size} 字节)`);

          // 清理和格式化HTML用于本地下载
          const cleanedHtml = this.cleanHtmlForDownload(html, title || '微信文章');

          return { html, size, cleanedHtml };
        } catch (error) {
          lastError = error as Error;
          console.warn(`❌ 下载失败 (代理: ${proxy.name}, 尝试: ${attempt + 1}): ${lastError.message}`);
          
          // 如果不是最后一次尝试，等待一段时间再重试
          if (attempt < maxRetries - 1) {
            await this.delay(1000 * (attempt + 1)); // 递增延迟
          }
        }
      }
    }
    
    // 如果所有代理都失败，尝试直连
    try {
      console.log(`🔄 尝试直连下载文章 "${title}"`);
      const response = await this.downloadWithProxy(articleUrl, undefined, options);
      const html = await response.text();
      
      if (!this.validateArticleHTML(html, title)) {
        throw new Error('文章HTML不完整或已被删除');
      }
      
      const size = new Blob([html]).size;
      console.log(`✅ 文章 "${title}" 直连下载成功 (大小: ${size} 字节)`);
      
      return { html, size };
    } catch (error) {
      lastError = error as Error;
    }
    
    throw new Error(`下载文章失败: ${lastError?.message || '未知错误'}`);
  }

  /**
   * 验证文章HTML是否完整
   */
  private validateArticleHTML(html: string, title?: string): boolean {
    try {
      // 检查是否包含文章内容区域
      const hasContent = html.includes('#js_content') || html.includes('js_content');
      
      // 检查是否是删除页面
      const isDeleted = html.includes('#js_fullscreen_layout_padding') && 
                       !html.includes('#js_content');
      
      if (isDeleted) {
        console.log(`⚠️ 文章 "${title}" 已被删除`);
        return false;
      }
      
      if (!hasContent) {
        console.log(`⚠️ 文章 "${title}" HTML不完整`);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('验证HTML时出错:', error);
      return false;
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 处理HTML内容中的图片URL
   * 将微信图片URL替换为代理URL，以便在本地HTML中正常显示
   */
  private processImageUrls(content: string): string {
    // 微信图片域名列表
    const wechatImageDomains = [
      'mmbiz.qpic.cn',
      'mmbiz.qlogo.cn',
      'wx.qlogo.cn',
      'thirdwx.qlogo.cn',
      'mp.weixin.qq.com'
    ];

    // 替换img标签中的src属性
    content = content.replace(/<img([^>]*)\s+src=["']([^"']+)["']([^>]*)>/gi, (match, before, src, after) => {
      // 检查是否为微信图片URL
      const isWechatImage = wechatImageDomains.some(domain => src.includes(domain));

      if (isWechatImage && !src.startsWith('/api/proxy/wechat-image')) {
        // 替换为代理URL
        const proxyUrl = `/api/proxy/wechat-image?url=${encodeURIComponent(src)}`;
        return `<img${before} src="${proxyUrl}"${after}>`;
      }

      return match;
    });

    // 替换CSS中的background-image
    content = content.replace(/background-image:\s*url\(["']?([^"')]+)["']?\)/gi, (match, url) => {
      const isWechatImage = wechatImageDomains.some(domain => url.includes(domain));

      if (isWechatImage && !url.startsWith('/api/proxy/wechat-image')) {
        const proxyUrl = `/api/proxy/wechat-image?url=${encodeURIComponent(url)}`;
        return `background-image: url("${proxyUrl}")`;
      }

      return match;
    });

    return content;
  }

  /**
   * 下载图片并转换为base64
   * 用于创建完全离线的HTML文件
   */
  private async downloadImageAsBase64(imageUrl: string): Promise<string | null> {
    try {
      console.log(`📷 下载图片: ${imageUrl}`);

      // 使用代理下载图片
      const response = await this.downloadWithProxy(imageUrl, undefined, { timeout: 10 });

      if (!response.ok) {
        console.warn(`❌ 图片下载失败: ${imageUrl} (${response.status})`);
        return null;
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const contentType = response.headers.get('content-type') || 'image/jpeg';

      // 转换为base64
      const base64 = buffer.toString('base64');
      const dataUrl = `data:${contentType};base64,${base64}`;

      console.log(`✅ 图片下载成功: ${imageUrl} (${buffer.length} bytes)`);
      return dataUrl;
    } catch (error) {
      console.warn(`❌ 图片下载失败: ${imageUrl}`, error);
      return null;
    }
  }

  /**
   * 处理HTML内容中的图片URL并下载图片为base64
   * 创建完全离线的HTML文件
   */
  private async processImageUrlsWithDownload(content: string): Promise<string> {
    // 微信图片域名列表
    const wechatImageDomains = [
      'mmbiz.qpic.cn',
      'mmbiz.qlogo.cn',
      'wx.qlogo.cn',
      'thirdwx.qlogo.cn',
      'mp.weixin.qq.com'
    ];

    // 提取所有图片URL
    const imageUrls: string[] = [];
    const imgRegex = /<img[^>]*\s+src=["']([^"']+)["'][^>]*>/gi;
    let match;

    while ((match = imgRegex.exec(content)) !== null) {
      const src = match[1];
      const isWechatImage = wechatImageDomains.some(domain => src.includes(domain));
      if (isWechatImage) {
        imageUrls.push(src);
      }
    }

    // 下载所有图片
    const imageMap = new Map<string, string>();
    for (const url of imageUrls) {
      const base64Data = await this.downloadImageAsBase64(url);
      if (base64Data) {
        imageMap.set(url, base64Data);
      }
      // 添加延迟避免请求过于频繁
      await this.delay(500);
    }

    // 替换HTML中的图片URL
    content = content.replace(/<img([^>]*)\s+src=["']([^"']+)["']([^>]*)>/gi, (match, before, src, after) => {
      if (imageMap.has(src)) {
        return `<img${before} src="${imageMap.get(src)}"${after}>`;
      }
      return match;
    });

    return content;
  }

  /**
   * 创建离线HTML文件
   * @param html 原始HTML内容
   * @param title 文章标题
   * @param downloadImages 是否下载图片到本地（转为base64）
   */
  public async createOfflineHtml(html: string, title: string, downloadImages: boolean = false): Promise<string> {
    try {
      // 提取文章内容区域
      let content = '';

      // 尝试提取文章标题
      const titleMatch = html.match(/<h1[^>]*class="[^"]*rich_media_title[^"]*"[^>]*>([\s\S]*?)<\/h1>/);
      const articleTitle = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : title;

      // 尝试提取文章内容
      const contentMatch = html.match(/<div[^>]*id="js_content"[^>]*>([\s\S]*?)<\/div>/);
      if (contentMatch) {
        content = contentMatch[1];
      } else {
        // 备用方案：查找其他可能的内容容器
        const altContentMatch = html.match(/<div[^>]*class="[^"]*rich_media_content[^"]*"[^>]*>([\s\S]*?)<\/div>/);
        if (altContentMatch) {
          content = altContentMatch[1];
        } else {
          content = '<p>无法提取文章内容</p>';
        }
      }

      // 处理图片URL
      if (downloadImages) {
        console.log('🖼️ 开始下载文章中的图片...');
        content = await this.processImageUrlsWithDownload(content);
        console.log('✅ 图片下载完成');
      } else {
        // 只替换为代理URL
        content = this.processImageUrls(content);
      }

      // 创建完整的HTML文档
      const offlineHtml = this.generateOfflineHtmlTemplate(articleTitle, content, downloadImages);

      return offlineHtml;
    } catch (error) {
      console.error('创建离线HTML时出错:', error);
      // 返回基本的HTML结构
      return this.generateErrorHtml(title, error instanceof Error ? error.message : '处理失败');
    }
  }

  /**
   * 生成离线HTML模板
   */
  private generateOfflineHtmlTemplate(title: string, content: string, hasLocalImages: boolean): string {
    const imageNotice = hasLocalImages
      ? '<div class="image-notice"><strong>📷 离线模式：</strong> 本文章的图片已下载到本地，可以离线查看。</div>'
      : '<div class="image-notice"><strong>📷 在线模式：</strong> 本文章中的图片需要网络连接才能正常显示。如果图片无法显示，请检查网络连接。</div>';

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        .article-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .article-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
            color: #1a1a1a;
        }
        .article-meta {
            color: #666;
            font-size: 14px;
        }
        .article-content {
            font-size: 16px;
            line-height: 1.8;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .article-content p {
            margin: 16px 0;
            text-align: justify;
        }
        .article-content h1, .article-content h2, .article-content h3 {
            margin: 24px 0 16px 0;
            color: #1a1a1a;
        }
        .download-info {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #999;
            text-align: center;
        }
        .image-notice {
            background-color: ${hasLocalImages ? '#e8f5e8' : '#f8f9fa'};
            border: 1px solid ${hasLocalImages ? '#c3e6c3' : '#e9ecef'};
            border-radius: 4px;
            padding: 12px;
            margin: 20px 0;
            font-size: 14px;
            color: ${hasLocalImages ? '#2d5a2d' : '#6c757d'};
        }
        .image-notice strong {
            color: ${hasLocalImages ? '#1e3a1e' : '#495057'};
        }
    </style>
</head>
<body>
    <div class="article-header">
        <h1 class="article-title">${title}</h1>
        <div class="article-meta">
            下载时间: ${new Date().toLocaleString('zh-CN')} | 模式: ${hasLocalImages ? '离线版本' : '在线版本'}
        </div>
    </div>

    ${imageNotice}

    <div class="article-content">
        ${content}
    </div>

    <div class="download-info">
        本文章由 FeedWe 下载保存 | ${new Date().toLocaleString('zh-CN')} | ${hasLocalImages ? '包含本地图片' : '需要网络连接查看图片'}
    </div>
</body>
</html>`;
  }

  /**
   * 生成错误HTML
   */
  private generateErrorHtml(title: string, errorMessage: string): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .error { color: #d32f2f; background: #ffebee; padding: 20px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>${title}</h1>
    <div class="error">
        <h3>处理失败</h3>
        <p>${errorMessage}</p>
        <p>请重试下载或联系管理员。</p>
    </div>
    <div style="margin-top: 40px; font-size: 12px; color: #999;">
        下载时间: ${new Date().toLocaleString('zh-CN')}
    </div>
</body>
</html>`;
  }

  /**
   * 批量下载文章
   */
  public async downloadArticles(
    articles: Array<{ id: string; url: string; title: string }>,
    userId: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<DownloadResult[]> {
    const results: DownloadResult[] = [];
    
    for (let i = 0; i < articles.length; i++) {
      const article = articles[i];
      
      try {
        // 创建下载记录
        const downloadRecord = await prisma.articleDownload.create({
          data: {
            articleId: article.id,
            userId: userId,
            status: 'DOWNLOADING'
          }
        });
        
        // 下载文章
        const { html, size } = await this.downloadArticleHTML(article.url, article.title);
        
        // 这里可以保存HTML到文件系统或对象存储
        const filePath = await this.saveArticleHTML(html, article.id, article.title);
        
        // 更新下载记录
        await prisma.articleDownload.update({
          where: { id: downloadRecord.id },
          data: {
            status: 'COMPLETED',
            filePath: filePath,
            fileSize: size,
            downloadTime: new Date()
          }
        });
        
        results.push({
          success: true,
          filePath,
          fileSize: size
        });
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        
        // 更新下载记录为失败状态
        await prisma.articleDownload.updateMany({
          where: {
            articleId: article.id,
            userId: userId,
            status: 'DOWNLOADING'
          },
          data: {
            status: 'FAILED',
            errorMessage: errorMessage
          }
        });
        
        results.push({
          success: false,
          errorMessage
        });
      }
      
      // 通知进度
      onProgress?.(i + 1, articles.length);
      
      // 批次间延迟，避免请求过于频繁
      if (i < articles.length - 1) {
        await this.delay(2000);
      }
    }
    
    return results;
  }

  /**
   * 保存文章HTML到文件系统
   */
  private async saveArticleHTML(html: string, articleId: string, title: string): Promise<string> {
    // 这里应该实现文件保存逻辑
    // 可以保存到本地文件系统、云存储等
    // 暂时返回一个模拟的文件路径
    const fileName = `${articleId}_${Date.now()}.html`;
    const filePath = `downloads/${fileName}`;

    // TODO: 实际的文件保存逻辑
    console.log(`💾 保存文章HTML: ${title} -> ${filePath}`);

    return filePath;
  }

  /**
   * 清理HTML用于本地下载
   */
  private cleanHtmlForDownload(html: string, title: string): string {
    try {
      // 提取文章内容区域
      let content = '';

      // 尝试提取文章标题
      const titleMatch = html.match(/<h1[^>]*class="[^"]*rich_media_title[^"]*"[^>]*>([\s\S]*?)<\/h1>/);
      const articleTitle = titleMatch ? titleMatch[1].replace(/<[^>]*>/g, '').trim() : title;

      // 尝试提取文章内容
      const contentMatch = html.match(/<div[^>]*id="js_content"[^>]*>([\s\S]*?)<\/div>/);
      if (contentMatch) {
        content = contentMatch[1];
      } else {
        // 备用方案：查找其他可能的内容容器
        const altContentMatch = html.match(/<div[^>]*class="[^"]*rich_media_content[^"]*"[^>]*>([\s\S]*?)<\/div>/);
        if (altContentMatch) {
          content = altContentMatch[1];
        } else {
          content = '<p>无法提取文章内容</p>';
        }
      }

      // 处理图片URL - 将微信图片URL替换为代理URL（推荐方案）
      content = this.processImageUrls(content);

      // 创建完整的HTML文档
      const cleanedHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${articleTitle}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        .article-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .article-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
            color: #1a1a1a;
        }
        .article-meta {
            color: #666;
            font-size: 14px;
        }
        .article-content {
            font-size: 16px;
            line-height: 1.8;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
        }
        .article-content p {
            margin: 16px 0;
        }
        .article-content h1, .article-content h2, .article-content h3 {
            margin: 24px 0 16px 0;
            font-weight: bold;
        }
        .article-content blockquote {
            border-left: 4px solid #ddd;
            margin: 16px 0;
            padding: 0 16px;
            color: #666;
        }
        .download-info {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #999;
            text-align: center;
        }
        .image-notice {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            margin: 20px 0;
            font-size: 14px;
            color: #6c757d;
        }
        .image-notice strong {
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="article-header">
        <h1 class="article-title">${articleTitle}</h1>
        <div class="article-meta">
            下载时间: ${new Date().toLocaleString('zh-CN')}
        </div>
    </div>

    <div class="article-content">
        ${content}
    </div>

    <div class="download-info">
        本文章由 FeedWe 下载保存 | ${new Date().toLocaleString('zh-CN')}
    </div>
</body>
</html>`;

      return cleanedHtml;
    } catch (error) {
      console.error('清理HTML时出错:', error);
      // 返回基本的HTML结构
      return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
</head>
<body>
    <h1>${title}</h1>
    <p>文章内容处理失败，请重试下载。</p>
    <div style="margin-top: 40px; font-size: 12px; color: #999;">
        下载时间: ${new Date().toLocaleString('zh-CN')}
    </div>
</body>
</html>`;
    }
  }
}
