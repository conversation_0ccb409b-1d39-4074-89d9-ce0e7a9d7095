import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../lib/auth';

const prisma = new PrismaClient();

// 获取代理服务器列表
export async function GET(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const proxyServers = await prisma.proxyServer.findMany({
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json({ proxyServers });
  } catch (error) {
    console.error('获取代理服务器列表失败:', error);
    return NextResponse.json({ error: '获取代理服务器列表失败' }, { status: 500 });
  }
}

// 创建代理服务器
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { name, url, isActive = true, priority = 0, maxRetries = 3, timeout = 30 } = body;

    if (!name || !url) {
      return NextResponse.json({ error: '名称和URL不能为空' }, { status: 400 });
    }

    // 验证URL格式
    try {
      new URL(url);
    } catch {
      return NextResponse.json({ error: 'URL格式不正确' }, { status: 400 });
    }

    const proxyServer = await prisma.proxyServer.create({
      data: {
        name,
        url,
        isActive,
        priority,
        maxRetries,
        timeout
      }
    });

    return NextResponse.json({ proxyServer }, { status: 201 });
  } catch (error) {
    console.error('创建代理服务器失败:', error);
    return NextResponse.json({ error: '创建代理服务器失败' }, { status: 500 });
  }
}
