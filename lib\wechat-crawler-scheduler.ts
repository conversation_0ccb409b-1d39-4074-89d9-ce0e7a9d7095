import { WechatCrawlerService } from './wechat-crawler-service';
import { formatBeijingTime } from './beijing-time';

/**
 * 微信公众号文章定时爬取调度器
 */
export class WechatCrawlerScheduler {
  private static instance: WechatCrawlerScheduler;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 5;

  private constructor() {}

  public static getInstance(): WechatCrawlerScheduler {
    if (!WechatCrawlerScheduler.instance) {
      WechatCrawlerScheduler.instance = new WechatCrawlerScheduler();
    }
    return WechatCrawlerScheduler.instance;
  }

  /**
   * 启动定时任务
   */
  public start(): void {
    if (this.intervalId) {
      console.log('⚠️ 定时任务已经在运行中');
      return;
    }

    console.log('🚀 启动微信公众号文章定时爬取调度器');

    // 立即执行一次
    this.executeTask();

    // 设置定时任务，每2分钟执行一次
    this.intervalId = setInterval(() => {
      this.executeTask();
    }, 2 * 60 * 1000); // 2分钟

    console.log('✅ 定时任务已启动，每30分钟执行一次');
  }

  /**
   * 停止定时任务
   */
  public stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('🛑 定时任务已停止');
    } else {
      console.log('⚠️ 定时任务未在运行');
    }
  }

  /**
   * 执行爬取任务
   */
  private async executeTask(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 上一个任务还在执行中，跳过本次执行');
      return;
    }

    // 检查连续失败次数
    if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
      console.log(`❌ 连续失败 ${this.consecutiveFailures} 次，已达到最大限制 ${this.maxConsecutiveFailures} 次，停止定时任务`);
      this.stop();
      return;
    }

    this.isRunning = true;
    console.log('🔄 开始执行定时爬取任务:', formatBeijingTime());

    try {
      // 使用共享的爬取服务
      const crawlerService = WechatCrawlerService.getInstance();
      const result = await crawlerService.executeCrawlTask();

      if (result.success) {
        // 重置连续失败计数
        this.consecutiveFailures = 0;
        console.log('✅ 定时爬取任务执行成功');

        if (result.stats) {
          console.log(`📊 统计信息: 处理 ${result.stats.totalAccounts} 个公众号，新增 ${result.stats.totalNewArticles} 篇文章`);
        }
      } else {
        // 增加连续失败计数
        this.consecutiveFailures++;
        console.error('❌ 定时爬取任务执行失败:', result.message);
        console.log(`⚠️ 连续失败次数: ${this.consecutiveFailures}/${this.maxConsecutiveFailures}`);
      }

    } catch (error) {
      console.error('❌ 定时爬取任务执行异常:', error);
      this.consecutiveFailures++;
      console.log(`⚠️ 连续失败次数: ${this.consecutiveFailures}/${this.maxConsecutiveFailures}`);
    } finally {
      this.isRunning = false;
      console.log('✅ 定时爬取任务执行完成:', new Date());
    }
  }

  /**
   * 手动执行一次爬取任务
   */
  public async executeOnce(): Promise<{ success: boolean; message: string; stats?: any }> {
    console.log('🔄 手动执行爬取任务');

    try {
      const crawlerService = WechatCrawlerService.getInstance();
      const result = await crawlerService.executeCrawlTask();

      if (result.success) {
        // 重置连续失败计数
        this.consecutiveFailures = 0;
      } else {
        this.consecutiveFailures++;
      }

      return result;
    } catch (error) {
      this.consecutiveFailures++;
      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 重置连续失败计数
   */
  public resetConsecutiveFailures(): void {
    this.consecutiveFailures = 0;
    console.log('🔄 连续失败计数已重置');
  }

  /**
   * 重置失败计数（别名方法，兼容旧API）
   */
  public resetFailureCount(): void {
    this.resetConsecutiveFailures();
  }

  /**
   * 获取当前状态
   */
  public getStatus(): {
    isRunning: boolean;
    isScheduled: boolean;
    consecutiveFailures: number;
    maxConsecutiveFailures: number;
  } {
    return {
      isRunning: this.isRunning,
      isScheduled: this.intervalId !== null,
      consecutiveFailures: this.consecutiveFailures,
      maxConsecutiveFailures: this.maxConsecutiveFailures
    };
  }
}