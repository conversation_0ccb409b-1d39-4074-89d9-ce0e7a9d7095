#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🏗️ Windows构建脚本启动...');

const options = {
  stdio: 'inherit',
  timeout: 300000 // 5分钟超时
};

async function main() {
try {
  // 清理可能存在的锁定文件
  console.log('🧹 清理临时文件...');
  const prismaPath = path.join(__dirname, '..', 'node_modules', '.prisma');
  if (fs.existsSync(prismaPath)) {
    try {
      // 在Windows上，先尝试温和地删除
      execSync(`rmdir /s /q "${prismaPath}"`, { stdio: 'ignore' });
    } catch (e) {
      console.log('⚠️ 无法删除.prisma目录，继续...');
    }
  }

  // 等待一下确保文件系统释放
  console.log('⏳ 等待文件系统释放...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('📦 生成Prisma客户端...');
  let generateAttempts = 0;
  const maxAttempts = 3;
  
  while (generateAttempts < maxAttempts) {
    try {
      execSync('npx prisma generate', options);
      break;
    } catch (error) {
      generateAttempts++;
      if (generateAttempts >= maxAttempts) {
        throw error;
      }
      console.log(`⚠️ 生成失败，重试 ${generateAttempts}/${maxAttempts}...`);
      // 等待更长时间
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  console.log('🗄️ 运行数据库迁移...');
  execSync('npx prisma migrate deploy', options);

  console.log('🏗️ 构建Next.js应用...');
  execSync('npm run build:next', options);

  console.log('✅ 构建完成！');
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
}

// 辅助函数：Promise化的setTimeout
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行主函数
main();
