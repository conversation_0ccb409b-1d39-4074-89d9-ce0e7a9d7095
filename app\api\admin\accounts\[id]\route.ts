import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';

// 更新公众号信息
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const { name, description, avatar } = await request.json();
    const params = await context.params;
    const accountId = params.id;

    // 更新公众号信息
    const updatedAccount = await prisma.wechatAccount.update({
      where: { id: accountId },
      data: {
        ...(name && { name }),
        ...(description && { description }),
        ...(avatar && { avatar })
      },
      include: {
        _count: {
          select: {
            subscriptions: true,
            articles: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      account: updatedAccount
    });
  } catch (error) {
    console.error('更新公众号失败:', error);
    const errorMessage = error instanceof Error ? error.message : '更新公众号失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 删除公众号
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const params = await context.params;
    const accountId = params.id;

    // 检查是否有活跃订阅
    const activeSubscriptions = await prisma.subscription.count({
      where: {
        wechatAccountId: accountId,
        isActive: true
      }
    });

    if (activeSubscriptions > 0) {
      return NextResponse.json(
        { error: `该公众号还有 ${activeSubscriptions} 个活跃订阅，无法删除` },
        { status: 400 }
      );
    }

    // 删除公众号及相关数据
    await prisma.$transaction(async (tx) => {
      // 删除相关文章
      await tx.article.deleteMany({
        where: { wechatAccountId: accountId }
      });

      // 删除相关订阅
      await tx.subscription.deleteMany({
        where: { wechatAccountId: accountId }
      });

      // 删除公众号
      await tx.wechatAccount.delete({
        where: { id: accountId }
      });
    });

    return NextResponse.json({
      success: true,
      message: '公众号删除成功'
    });
  } catch (error) {
    console.error('删除公众号失败:', error);
    const errorMessage = error instanceof Error ? error.message : '删除公众号失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
