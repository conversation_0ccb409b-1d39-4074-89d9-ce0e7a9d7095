'use client';

import React from 'react';

export default function SEOContent() {
  return (
    <section className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            为什么选择FeedWe微信公众号订阅工具？
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            FeedWe是专业的微信公众号订阅管理平台，为个人用户和企业团队提供高效的内容聚合解决方案
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* 功能特色 */}
          <div className="text-center">
            <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">批量订阅管理</h3>
            <p className="text-gray-600">
              支持批量添加微信公众号订阅，一键管理所有关注的公众号，提高订阅效率
            </p>
          </div>

          <div className="text-center">
            <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 0 0-15 0v5h5l-5 5-5-5h5V7a10 10 0 0 1 20 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">智能文章推送</h3>
            <p className="text-gray-600">
              新文章自动推送到企业微信、钉钉、飞书，团队第一时间获取重要资讯
            </p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">历史文章查看</h3>
            <p className="text-gray-600">
              完整保存公众号历史文章，支持搜索、筛选、导出，方便回顾和整理
            </p>
          </div>

          <div className="text-center">
            <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">安全可靠</h3>
            <p className="text-gray-600">
              企业级安全保障，数据加密存储，保护您的订阅信息和阅读隐私
            </p>
          </div>

          <div className="text-center">
            <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">高性价比</h3>
            <p className="text-gray-600">
              每个公众号仅需2元/月，相比其他工具节省80%成本，性价比超高
            </p>
          </div>

          <div className="text-center">
            <div className="bg-indigo-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">简单易用</h3>
            <p className="text-gray-600">
              界面简洁直观，操作简单易懂，无需技术背景即可快速上手使用
            </p>
          </div>
        </div>

        {/* 关键词密度优化内容 */}
        <div className="mt-16 bg-gray-50 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            FeedWe - 您的微信公众号订阅管理专家
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">适用场景</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 个人用户：管理关注的微信公众号，提高阅读效率</li>
                <li>• 企业团队：获取行业资讯，保持信息同步</li>
                <li>• 内容创作者：收集素材，跟踪热点话题</li>
                <li>• 研究人员：追踪特定领域的公众号内容</li>
                <li>• 媒体工作者：监控竞品动态，获取新闻线索</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">核心优势</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• 支持所有类型的微信公众号订阅</li>
                <li>• 实时同步最新文章，不错过重要内容</li>
                <li>• 多平台推送集成，提高工作效率</li>
                <li>• 完整的文章历史记录和搜索功能</li>
                <li>• 专业的客户服务和技术支持</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
