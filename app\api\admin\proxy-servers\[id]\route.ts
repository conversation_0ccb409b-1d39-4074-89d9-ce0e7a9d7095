import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../../lib/auth';

const prisma = new PrismaClient();

// 更新代理服务器
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromRequest(request);

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, url, isActive, priority, maxRetries, timeout } = body;

    // 验证代理服务器是否存在
    const existingProxy = await prisma.proxyServer.findUnique({
      where: { id }
    });

    if (!existingProxy) {
      return NextResponse.json({ error: '代理服务器不存在' }, { status: 404 });
    }

    // 验证URL格式（如果提供了URL）
    if (url) {
      try {
        new URL(url);
      } catch {
        return NextResponse.json({ error: 'URL格式不正确' }, { status: 400 });
      }
    }

    const updatedProxy = await prisma.proxyServer.update({
      where: { id },
      data: {
        ...(name !== undefined && { name }),
        ...(url !== undefined && { url }),
        ...(isActive !== undefined && { isActive }),
        ...(priority !== undefined && { priority }),
        ...(maxRetries !== undefined && { maxRetries }),
        ...(timeout !== undefined && { timeout })
      }
    });

    return NextResponse.json({ proxyServer: updatedProxy });
  } catch (error) {
    console.error('更新代理服务器失败:', error);
    return NextResponse.json({ error: '更新代理服务器失败' }, { status: 500 });
  }
}

// 删除代理服务器
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromRequest(request);

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { id } = await params;

    // 验证代理服务器是否存在
    const existingProxy = await prisma.proxyServer.findUnique({
      where: { id }
    });

    if (!existingProxy) {
      return NextResponse.json({ error: '代理服务器不存在' }, { status: 404 });
    }

    // 检查是否有正在使用该代理的下载任务
    const activeDownloads = await prisma.articleDownload.count({
      where: {
        proxyServerId: id,
        status: 'DOWNLOADING'
      }
    });

    if (activeDownloads > 0) {
      return NextResponse.json({ 
        error: '该代理服务器正在被使用，无法删除' 
      }, { status: 400 });
    }

    await prisma.proxyServer.delete({
      where: { id }
    });

    return NextResponse.json({ message: '代理服务器删除成功' });
  } catch (error) {
    console.error('删除代理服务器失败:', error);
    return NextResponse.json({ error: '删除代理服务器失败' }, { status: 500 });
  }
}

// 获取单个代理服务器详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromRequest(request);

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { id } = await params;

    const proxyServer = await prisma.proxyServer.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            downloads: true
          }
        }
      }
    });

    if (!proxyServer) {
      return NextResponse.json({ error: '代理服务器不存在' }, { status: 404 });
    }

    return NextResponse.json({ proxyServer });
  } catch (error) {
    console.error('获取代理服务器详情失败:', error);
    return NextResponse.json({ error: '获取代理服务器详情失败' }, { status: 500 });
  }
}
