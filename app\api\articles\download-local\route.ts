import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../lib/auth';
import { ArticleDownloader } from '../../../../lib/article-downloader';
import JSZip from 'jszip';

const prisma = new PrismaClient();

// 批量下载文章到本地（ZIP格式）
export async function POST(request: NextRequest) {
  try {
    const user = await getUserFromRequest(request);
    
    if (!user) {
      return NextResponse.json({ error: '请先登录' }, { status: 401 });
    }

    const body = await request.json();
    const { articleIds, options = {} } = body;

    if (!articleIds || !Array.isArray(articleIds) || articleIds.length === 0) {
      return NextResponse.json({ error: '文章ID列表不能为空' }, { status: 400 });
    }

    if (articleIds.length > 20) {
      return NextResponse.json({ error: '单次最多下载20篇文章到本地' }, { status: 400 });
    }

    // 获取文章信息
    const articles = await prisma.article.findMany({
      where: { 
        id: { in: articleIds }
      },
      include: {
        wechatAccount: true
      }
    });

    if (articles.length === 0) {
      return NextResponse.json({ error: '未找到有效文章' }, { status: 404 });
    }

    // 检查用户订阅权限
    const wechatAccountIds = [...new Set(articles.map(a => a.wechatAccountId))];
    const subscriptions = await prisma.subscription.findMany({
      where: {
        userId: user.id,
        wechatAccountId: { in: wechatAccountIds },
        isActive: true
      }
    });

    const subscribedAccountIds = new Set(subscriptions.map(s => s.wechatAccountId));
    const validArticles = articles.filter(a => subscribedAccountIds.has(a.wechatAccountId));

    if (validArticles.length === 0) {
      return NextResponse.json({ error: '您未订阅这些文章的公众号' }, { status: 403 });
    }

    // 下载文章
    const downloader = new ArticleDownloader();
    const downloadResults = [];
    
    for (const article of validArticles) {
      try {
        console.log(`📥 开始下载文章: ${article.title}`);
        const { cleanedHtml } = await downloader.downloadArticleHTML(article.url, article.title, options);
        
        downloadResults.push({
          success: true,
          article: {
            id: article.id,
            title: article.title,
            html: cleanedHtml,
            accountName: article.wechatAccount.name
          }
        });
        
        // 添加延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.error(`❌ 下载文章失败: ${article.title}`, error);
        downloadResults.push({
          success: false,
          article: {
            id: article.id,
            title: article.title,
            error: error instanceof Error ? error.message : '下载失败'
          }
        });
      }
    }

    const successfulDownloads = downloadResults.filter(r => r.success);
    
    if (successfulDownloads.length === 0) {
      return NextResponse.json({ error: '所有文章下载失败' }, { status: 500 });
    }

    // 创建ZIP文件
    const zip = new JSZip();
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    
    successfulDownloads.forEach((result, index) => {
      if (result.success && result.article.html) {
        // 清理文件名
        const cleanFileName = result.article.title
          .replace(/[<>:"/\\|?*]/g, '')
          .replace(/\s+/g, '_')
          .substring(0, 50);
        
        const fileName = `${String(index + 1).padStart(2, '0')}_${cleanFileName}.html`;
        zip.file(fileName, result.article.html);
      }
    });

    // 添加说明文件
    const readmeContent = `
微信公众号文章下载包
===================

下载时间: ${new Date().toLocaleString('zh-CN')}
下载用户: ${user.email}
文章数量: ${successfulDownloads.length}

文章列表:
${successfulDownloads.map((result, index) => 
  `${index + 1}. ${result.article.title} (来源: ${result.article.accountName})`
).join('\n')}

使用说明:
- 每个HTML文件都是完整的网页，可以直接在浏览器中打开
- 文件已经过格式化，适合离线阅读
- 图片链接可能需要网络连接才能显示

由 FeedWe 生成 | ${new Date().toLocaleString('zh-CN')}
`;
    
    zip.file('README.txt', readmeContent);

    // 生成ZIP文件
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

    // 返回ZIP文件
    return new NextResponse(zipBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="wechat_articles_${timestamp}.zip"`,
        'Content-Length': zipBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('批量下载失败:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : '批量下载失败' 
    }, { status: 500 });
  }
}
