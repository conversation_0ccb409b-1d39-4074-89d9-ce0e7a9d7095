import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 并行查询公众号统计数据
    const [
      totalAccounts,
      activeAccounts,
      totalArticles,
      todayNewAccounts,
      popularAccounts
    ] = await Promise.all([
      // 总公众号数
      prisma.wechatAccount.count(),
      
      // 有订阅的公众号数
      prisma.wechatAccount.count({
        where: {
          subscriptions: {
            some: {
              isActive: true
            }
          }
        }
      }),
      
      // 总文章数
      prisma.article.count(),
      
      // 今日新增公众号
      prisma.wechatAccount.count({
        where: {
          createdAt: {
            gte: today,
            lt: tomorrow
          }
        }
      }),
      
      // 热门公众号（按订阅数排序）
      prisma.wechatAccount.findMany({
        include: {
          _count: {
            select: {
              subscriptions: true
            }
          }
        },
        orderBy: {
          subscriptions: {
            _count: 'desc'
          }
        },
        take: 5
      })
    ]);

    return NextResponse.json({
      totalAccounts,
      activeAccounts,
      totalArticles,
      todayNewAccounts,
      popularAccounts: popularAccounts.map(account => ({
        id: account.id,
        name: account.name,
        avatar: account.avatar && (
          account.avatar.includes('mmbiz.qpic.cn') ||
          account.avatar.includes('mmbiz.qlogo.cn') ||
          account.avatar.includes('wx.qlogo.cn') ||
          account.avatar.includes('thirdwx.qlogo.cn')
        ) ? `/api/proxy/wechat-image?url=${encodeURIComponent(account.avatar)}`
          : account.avatar,
        subscriptionCount: account._count.subscriptions
      }))
    });
  } catch (error) {
    console.error('获取公众号统计失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取公众号统计失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
