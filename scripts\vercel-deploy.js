#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🚀 开始Vercel部署流程...');

try {
  // 设置超时时间
  const options = {
    stdio: 'inherit',
    timeout: 300000 // 5分钟超时
  };

  console.log('📦 生成Prisma客户端...');
  try {
    execSync('npx prisma generate', options);
  } catch (generateError) {
    console.log('⚠️ 首次生成失败，清理后重试...');
    // 清理可能损坏的文件
    try {
      execSync('rm -rf node_modules/.prisma', { stdio: 'inherit' });
    } catch (e) {
      // 忽略清理错误
    }
    // 重试生成
    execSync('npx prisma generate', options);
  }

  console.log('🗄️ 运行数据库迁移...');
  // 使用直连URL进行迁移（优先级：DIRECT_URL > POSTGRES_URL_NON_POOLING > DATABASE_URL）
  const migrationUrl = process.env.DIRECT_URL ||
                      process.env.POSTGRES_URL_NON_POOLING ||
                      process.env.DATABASE_URL;

  if (migrationUrl && migrationUrl !== process.env.DATABASE_URL) {
    console.log('📡 使用直连URL进行迁移...');
    execSync('npx prisma migrate deploy', {
      ...options,
      env: {
        ...options.env,
        DATABASE_URL: migrationUrl
      }
    });
  } else {
    console.log('📡 使用默认URL进行迁移...');
    execSync('npx prisma migrate deploy', options);
  }

  console.log('🏗️ 构建Next.js应用...');
  execSync('npm run build', options);

  console.log('✅ 部署完成！');
} catch (error) {
  console.error('❌ 部署失败:', error.message);
  
  // 如果迁移失败，尝试只生成客户端和构建
  if (error.message.includes('migrate')) {
    console.log('⚠️ 迁移失败，尝试跳过迁移继续构建...');
    try {
      execSync('npm run build', options);
      console.log('✅ 构建完成（跳过迁移）');
    } catch (buildError) {
      console.error('❌ 构建也失败了:', buildError.message);
      process.exit(1);
    }
  } else {
    process.exit(1);
  }
}
