import { WebhookType } from '@prisma/client';

// 企业微信消息格式
interface WeWorkMessage {
  msgtype: 'text' | 'markdown' | 'image' | 'news';
  text?: {
    content: string;
    mentioned_list?: string[];
  };
  markdown?: {
    content: string;
  };
  image?: {
    base64: string;
    md5: string;
  };
  news?: {
    articles: Array<{
      title: string;
      description?: string;
      url?: string;
      picurl?: string;
    }>;
  };
}

// 钉钉消息格式
interface DingTalkMessage {
  msgtype: 'text' | 'markdown' | 'link';
  text?: {
    content: string;
  };
  markdown?: {
    title: string;
    text: string;
  };
  link?: {
    title: string;
    text: string;
    messageUrl: string;
    picUrl?: string;
  };
  at?: {
    atMobiles?: string[];
    isAtAll?: boolean;
  };
}

// 飞书消息格式
interface FeishuMessage {
  msg_type: 'text' | 'rich_text' | 'interactive';
  content: {
    text?: string;
    rich_text?: {
      elements: Array<{
        tag: string;
        text?: string;
        href?: string;
      }>;
    };
  };
}

// 文章信息接口
export interface ArticleInfo {
  title: string;
  url: string;
  summary?: string;
  publishDate: string;
  accountName: string;
  coverImage?: string;
  isMainArticle?: boolean; // 是否为主文章
}

// 副文章合并信息接口
export interface SubArticlesInfo {
  articles: Array<{
    title: string;
    url: string;
    summary?: string;
  }>;
  accountName: string;
  publishDate: string;
}

// 告警信息接口
export interface AlertInfo {
  title: string;
  message: string;
  timestamp: string;
  level: 'INFO' | 'WARNING' | 'ERROR';
}

/**
 * Webhook通知服务类
 */
export class WebhookNotificationService {
  /**
   * 发送新文章通知
   */
  static async sendNewArticleNotification(
    webhookUrl: string,
    webhookType: WebhookType,
    article: ArticleInfo
  ): Promise<boolean> {
    try {
      switch (webhookType) {
        case WebhookType.WEWORK:
          return await this.sendWeWorkNotification(webhookUrl, article);
        case WebhookType.DINGTALK:
          return await this.sendDingTalkNotification(webhookUrl, article);
        case WebhookType.FEISHU:
          return await this.sendFeishuNotification(webhookUrl, article);
        default:
          console.error('不支持的webhook类型:', webhookType);
          return false;
      }
    } catch (error) {
      console.error('发送webhook通知失败:', error);
      return false;
    }
  }

  /**
   * 发送企业微信通知
   */
  private static async sendWeWorkNotification(
    webhookUrl: string,
    article: ArticleInfo
  ): Promise<boolean> {
    if (article.isMainArticle) {
      // 主文章使用图文消息格式，更美观
      const message: WeWorkMessage = {
        msgtype: 'news',
        news: {
          articles: [{
            title: `${article.title}`,
            description: `${article.accountName}`,
            url: article.url,
            picurl: article.coverImage || 'https://images.unsplash.com/photo-*************-b95a79798f07?w=300&h=200&fit=crop'
          }]
        }
      };
      return await this.sendWebhookMessage(webhookUrl, message);
    } else {
      // 副文章使用文本消息提醒
      const content = `📄 副文章发布提醒

🏢 公众号：${article.accountName}
📝 标题：${article.title}
⏰ 发布时间：${new Date(article.publishDate).toLocaleString()}

🔗 阅读原文：${article.url}`;

      const message: WeWorkMessage = {
        msgtype: 'text',
        text: {
          content: content
        }
      };
      return await this.sendWebhookMessage(webhookUrl, message);
    }
  }

  /**
   * 发送钉钉通知
   */
  private static async sendDingTalkNotification(
    webhookUrl: string,
    article: ArticleInfo
  ): Promise<boolean> {
    const message: DingTalkMessage = {
      msgtype: 'markdown',
      markdown: {
        title: '新文章发布',
        text: `## 📰 新文章发布

**公众号**: ${article.accountName}  
**标题**: [${article.title}](${article.url})  
**发布时间**: ${new Date(article.publishDate).toLocaleString()}  
${article.summary ? `**摘要**: ${article.summary}  ` : ''}

点击标题查看完整文章`
      }
    };

    return await this.sendWebhookMessage(webhookUrl, message);
  }

  /**
   * 发送飞书通知
   */
  private static async sendFeishuNotification(
    webhookUrl: string,
    article: ArticleInfo
  ): Promise<boolean> {
    const message: FeishuMessage = {
      msg_type: 'rich_text',
      content: {
        rich_text: {
          elements: [
            {
              tag: 'text',
              text: '📰 新文章发布\n\n'
            },
            {
              tag: 'text',
              text: `公众号: ${article.accountName}\n`
            },
            {
              tag: 'a',
              text: article.title,
              href: article.url
            },
            {
              tag: 'text',
              text: `\n发布时间: ${new Date(article.publishDate).toLocaleString()}\n`
            },
            ...(article.summary ? [{
              tag: 'text' as const,
              text: `摘要: ${article.summary}\n`
            }] : []),
            {
              tag: 'text',
              text: '\n点击标题查看完整文章'
            }
          ]
        }
      }
    };

    return await this.sendWebhookMessage(webhookUrl, message);
  }

  /**
   * 发送webhook消息的通用方法
   */
  private static async sendWebhookMessage(
    webhookUrl: string,
    message: WeWorkMessage | DingTalkMessage | FeishuMessage
  ): Promise<boolean> {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message)
      });

      const result = await response.json();
      
      // 检查不同平台的成功响应格式
      if (result.errcode === 0 || result.code === 0 || result.StatusCode === 0 || response.ok) {
        return true;
      } else {
        console.error('Webhook消息发送失败:', result);
        return false;
      }
    } catch (error) {
      console.error('发送Webhook消息异常:', error);
      return false;
    }
  }

  /**
   * 发送副文章合并通知
   */
  static async sendSubArticlesNotification(
    webhookUrl: string,
    webhookType: WebhookType,
    subArticlesInfo: SubArticlesInfo
  ): Promise<boolean> {
    try {
      switch (webhookType) {
        case WebhookType.WEWORK:
          return await this.sendWeWorkSubArticlesNotification(webhookUrl, subArticlesInfo);
        case WebhookType.DINGTALK:
          return await this.sendDingTalkSubArticlesNotification(webhookUrl, subArticlesInfo);
        case WebhookType.FEISHU:
          return await this.sendFeishuSubArticlesNotification(webhookUrl, subArticlesInfo);
        default:
          console.error('不支持的webhook类型:', webhookType);
          return false;
      }
    } catch (error) {
      console.error('发送副文章合并通知失败:', error);
      return false;
    }
  }

  /**
   * 发送企业微信副文章合并通知（文本格式）
   */
  private static async sendWeWorkSubArticlesNotification(
    webhookUrl: string,
    subArticlesInfo: SubArticlesInfo
  ): Promise<boolean> {
    const articlesList = subArticlesInfo.articles.map((article, index) =>
      `${index + 1}. ${article.title}\n   ${article.url}`
    ).join('\n\n');

    const content = `${subArticlesInfo.accountName}还发布了${subArticlesInfo.articles.length}篇副文章

 文章列表：
${articlesList}`;

    const message: WeWorkMessage = {
      msgtype: 'text',
      text: {
        content: content
      }
    };

    return await this.sendWebhookMessage(webhookUrl, message);
  }

  /**
   * 发送钉钉副文章合并通知
   */
  private static async sendDingTalkSubArticlesNotification(
    webhookUrl: string,
    subArticlesInfo: SubArticlesInfo
  ): Promise<boolean> {
    const articlesList = subArticlesInfo.articles.map((article, index) =>
      `${index + 1}. [${article.title}](${article.url})`
    ).join('\n\n');

    const message: DingTalkMessage = {
      msgtype: 'markdown',
      markdown: {
        title: '副文章发布提醒',
        text: `## 📄 副文章发布提醒 (${subArticlesInfo.articles.length}篇)

**公众号**: ${subArticlesInfo.accountName}
**发布时间**: ${new Date(subArticlesInfo.publishDate).toLocaleString()}

**文章列表**:
${articlesList}`
      }
    };

    return await this.sendWebhookMessage(webhookUrl, message);
  }

  /**
   * 发送飞书副文章合并通知
   */
  private static async sendFeishuSubArticlesNotification(
    webhookUrl: string,
    subArticlesInfo: SubArticlesInfo
  ): Promise<boolean> {
    const elements = [
      {
        tag: 'text',
        text: `📄 副文章发布提醒 (${subArticlesInfo.articles.length}篇)\n\n`
      },
      {
        tag: 'text',
        text: `公众号: ${subArticlesInfo.accountName}\n发布时间: ${new Date(subArticlesInfo.publishDate).toLocaleString()}\n\n文章列表:\n`
      }
    ];

    // 添加文章链接
    subArticlesInfo.articles.forEach((article, index) => {
      elements.push({
        tag: 'text',
        text: `${index + 1}. ${article.title}\n   ${article.url}\n\n`
      });
    });

    const message: FeishuMessage = {
      msg_type: 'rich_text',
      content: {
        rich_text: {
          elements: elements
        }
      }
    };

    return await this.sendWebhookMessage(webhookUrl, message);
  }

  /**
   * 测试webhook连接
   */
  static async testWebhookConnection(
    webhookUrl: string,
    webhookType: WebhookType
  ): Promise<boolean> {
    // 测试主文章格式
    const mainTestArticle: ArticleInfo = {
      title: 'Webhook连接测试 - 主文章格式',
      url: 'https://feedwe.example.com/main',
      summary: '这是主文章格式的测试消息。主文章会使用更醒目的图文消息格式，包含封面图片和完整摘要。如果您收到此消息，说明webhook配置成功！',
      publishDate: new Date().toISOString(),
      accountName: 'FeedWe系统测试',
      coverImage: 'https://images.unsplash.com/photo-*************-b95a79798f07?w=300&h=200&fit=crop',
      isMainArticle: true
    };

    const mainResult = await this.sendNewArticleNotification(webhookUrl, webhookType, mainTestArticle);

    // 等待2秒后发送副文章合并测试
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试副文章合并格式
    const subArticlesTestInfo: SubArticlesInfo = {
      articles: [
        {
          title: 'Webhook连接测试 - 副文章1',
          url: 'https://feedwe.example.com/sub1',
          summary: '这是第一篇副文章测试'
        },
        {
          title: 'Webhook连接测试 - 副文章2',
          url: 'https://feedwe.example.com/sub2',
          summary: '这是第二篇副文章测试'
        }
      ],
      accountName: 'FeedWe系统测试',
      publishDate: new Date().toISOString()
    };

    const subResult = await this.sendSubArticlesNotification(webhookUrl, webhookType, subArticlesTestInfo);

    // 只要有一个成功就认为连接正常
    return mainResult || subResult;
  }

  /**
   * 发送告警通知
   */
  async sendAlert(webhook: any, alertInfo: AlertInfo): Promise<boolean> {
    try {
      const { url: webhookUrl, type: webhookType } = webhook;

      // 根据告警级别选择颜色和图标
      const levelConfig = {
        'INFO': { color: '#36a3f7', icon: 'ℹ️' },
        'WARNING': { color: '#ffb800', icon: '⚠️' },
        'ERROR': { color: '#f56565', icon: '🚨' }
      };

      const config = levelConfig[alertInfo.level] || levelConfig['INFO'];

      let message: any;

      switch (webhookType) {
        case WebhookType.WEWORK:
          message = {
            msgtype: 'markdown',
            markdown: {
              content: `${config.icon} **${alertInfo.title}**\n\n` +
                      `**级别**: ${alertInfo.level}\n` +
                      `**时间**: ${new Date(alertInfo.timestamp).toLocaleString('zh-CN')}\n` +
                      `**详情**: ${alertInfo.message}\n\n` +
                      `请及时检查系统状态。`
            }
          };
          break;

        case WebhookType.DINGTALK:
          message = {
            msgtype: 'markdown',
            markdown: {
              title: alertInfo.title,
              text: `${config.icon} **${alertInfo.title}**\n\n` +
                   `**级别**: ${alertInfo.level}\n` +
                   `**时间**: ${new Date(alertInfo.timestamp).toLocaleString('zh-CN')}\n` +
                   `**详情**: ${alertInfo.message}\n\n` +
                   `请及时检查系统状态。`
            }
          };
          break;

        case WebhookType.FEISHU:
          message = {
            msg_type: 'interactive',
            card: {
              config: {
                wide_screen_mode: true
              },
              elements: [
                {
                  tag: 'div',
                  text: {
                    content: `${config.icon} **${alertInfo.title}**`,
                    tag: 'lark_md'
                  }
                },
                {
                  tag: 'hr'
                },
                {
                  tag: 'div',
                  fields: [
                    {
                      is_short: true,
                      text: {
                        content: `**级别**\n${alertInfo.level}`,
                        tag: 'lark_md'
                      }
                    },
                    {
                      is_short: true,
                      text: {
                        content: `**时间**\n${new Date(alertInfo.timestamp).toLocaleString('zh-CN')}`,
                        tag: 'lark_md'
                      }
                    }
                  ]
                },
                {
                  tag: 'div',
                  text: {
                    content: `**详情**\n${alertInfo.message}`,
                    tag: 'lark_md'
                  }
                }
              ],
              header: {
                template: config.color,
                title: {
                  content: '系统告警通知',
                  tag: 'plain_text'
                }
              }
            }
          };
          break;

        default:
          console.error('不支持的webhook类型:', webhookType);
          return false;
      }

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      if (response.ok) {
        console.log(`✅ 告警通知发送成功 (${webhookType})`);
        return true;
      } else {
        const errorText = await response.text();
        console.error(`❌ 告警通知发送失败 (${webhookType}):`, response.status, errorText);
        return false;
      }
    } catch (error) {
      console.error('❌ 发送告警通知时出错:', error);
      return false;
    }
  }
}
