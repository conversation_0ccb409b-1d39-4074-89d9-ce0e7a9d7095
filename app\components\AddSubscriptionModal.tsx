'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from './AuthProvider';

type Account = {
  id: string;
  name: string;
  avatar: string;
  openid: string;
  description?: string;
};

interface AddSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AddSubscriptionModal({ isOpen, onClose }: AddSubscriptionModalProps) {
  const { user, token } = useAuth();
  const [search, setSearch] = useState('');
  const [results, setResults] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [subscribing, setSubscribing] = useState<string | null>(null);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error'>('success');

  // 重置状态当弹窗关闭时
  useEffect(() => {
    if (!isOpen) {
      setSearch('');
      setResults([]);
      setMessage('');
      setSubscribing(null);
    }
  }, [isOpen]);

  // 搜索公众号
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!search.trim()) return;
    
    setLoading(true);
    setMessage('');
    setResults([]);
    
    try {
      const res = await fetch('/api/search-wechat-account', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: search }),
      });
      const data = await res.json();
      setResults(data);
    } catch (error) {
      setMessage('搜索失败，请重试');
      setMessageType('error');
    } finally {
      setLoading(false);
    }
  };

  // 订阅公众号
  const handleSubscribe = async (account: Account) => {
    if (!user || !token) {
      setMessage('请先登录');
      setMessageType('error');
      return;
    }

    setSubscribing(account.id);
    setMessage('');

    try {
      const res = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ wechatAccountId: account.id }),
      });

      if (res.ok) {
        setMessage('订阅成功！');
        setMessageType('success');
        // 从结果中移除已订阅的账号
        setResults(prev => prev.filter(acc => acc.id !== account.id));
      } else {
        const errorData = await res.json();
        setMessage(errorData.error || '订阅失败，请重试');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('订阅失败，请重试');
      setMessageType('error');
    } finally {
      setSubscribing(null);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden modal-content">
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">添加订阅</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 140px)' }}>
          {/* 搜索表单 */}
          <form onSubmit={handleSearch} className="mb-6">
            <div className="flex">
              <input
                value={search}
                onChange={e => setSearch(e.target.value)}
                placeholder="输入公众号名称进行搜索..."
                className="flex-1 border border-gray-300 rounded-l-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button 
                type="submit" 
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-r-lg transition-colors disabled:opacity-50"
                disabled={loading || !search.trim()}
              >
                {loading ? '搜索中...' : '搜索'}
              </button>
            </div>
          </form>

          {/* 消息提示 */}
          {message && (
            <div className={`mb-4 p-3 rounded-lg ${
              messageType === 'success' 
                ? 'bg-green-100 border border-green-400 text-green-700' 
                : 'bg-red-100 border border-red-400 text-red-700'
            }`}>
              {message}
            </div>
          )}

          {/* 搜索结果 */}
          {results.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">搜索结果</h3>
              {results.map(acc => (
                <div key={acc.openid} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      <img 
                        src={acc.avatar} 
                        alt={acc.name} 
                        className="w-12 h-12 rounded-full mr-4 flex-shrink-0"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate">{acc.name}</h4>
                        {acc.description && (
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            {acc.description}
                          </p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => handleSubscribe(acc)}
                      className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50 ml-4 flex-shrink-0"
                      disabled={subscribing === acc.id}
                    >
                      {subscribing === acc.id ? '订阅中...' : '订阅'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 空状态 */}
          {!loading && search && results.length === 0 && (
            <div className="text-center py-8">
              <div className="text-gray-500 text-lg">未找到相关公众号</div>
              <p className="text-gray-400 mt-2">请尝试其他关键词</p>
            </div>
          )}

          {/* 初始状态 */}
          {!search && results.length === 0 && (
            <div className="text-center py-8">
              <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <div className="text-gray-500 text-lg">搜索微信公众号</div>
              <p className="text-gray-400 mt-2">输入公众号名称开始搜索</p>
            </div>
          )}
        </div>

        {/* 弹窗底部 */}
        <div className="flex justify-end p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}
