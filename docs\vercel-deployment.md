# Vercel部署指南

## 问题说明

Vercel是无服务器(Serverless)平台，不支持长时间运行的后台定时任务。因此需要使用Vercel Cron Jobs来替代内置的定时任务。

## 解决方案

### 1. Vercel Cron Jobs配置

项目已配置Vercel Cron Jobs，会每2分钟自动执行一次文章爬取任务。

**配置文件**: `vercel.json`
```json
{
  "crons": [
    {
      "path": "/api/cron/crawl-articles",
      "schedule": "*/2 * * * *"
    }
  ]
}
```

**Cron端点**: `/api/cron/crawl-articles`
- 每2分钟执行一次
- 自动处理30分钟冷却期
- 只在6:00-23:00之间执行
- 每次最多处理3个公众号（避免超时）

### 2. 环境变量配置

在Vercel项目设置中添加以下环境变量：

#### 必需的环境变量
```bash
# 数据库连接
DATABASE_URL=mysql://username:password@host:port/database

# JWT密钥
JWT_SECRET=your-jwt-secret

# 微信凭证
WECHAT_TOKEN=your-wechat-token
WECHAT_COOKIE=your-wechat-cookie
WECHAT_DATA_TICKET=your-data-ticket

# Cron安全密钥（可选，用于验证Cron请求）
CRON_SECRET=your-cron-secret
```

#### 自动设置的环境变量
```bash
# Vercel会自动设置
VERCEL=1
NODE_ENV=production
VERCEL_REGION=iad1
VERCEL_URL=your-app.vercel.app
```

### 3. 部署步骤

1. **连接GitHub仓库**
   - 在Vercel控制台导入GitHub项目
   - 选择正确的分支（通常是main或master）

2. **配置环境变量**
   - 在项目设置 → Environment Variables中添加所需变量
   - 确保DATABASE_URL指向可访问的数据库

3. **部署项目**
   - Vercel会自动检测Next.js项目并部署
   - 构建命令：`npm run build`
   - 输出目录：`.next`

4. **验证部署**
   - 访问 `https://your-app.vercel.app/api/status` 检查状态
   - 确认Cron Jobs配置正确

### 4. 监控和调试

#### 检查应用状态
```bash
curl https://your-app.vercel.app/api/status
```

**响应示例**:
```json
{
  "success": true,
  "environment": {
    "isVercel": true,
    "isProduction": true,
    "vercelRegion": "iad1"
  },
  "scheduler": {
    "disabled": true,
    "recommendation": "Vercel环境建议使用Cron Jobs代替定时任务"
  },
  "cronJobs": {
    "available": true,
    "endpoint": "/api/cron/crawl-articles",
    "schedule": "每2分钟执行一次"
  }
}
```

#### 查看Cron执行日志
1. 进入Vercel项目控制台
2. 点击 "Functions" 标签
3. 找到 `/api/cron/crawl-articles` 函数
4. 查看执行日志和错误信息

#### 手动触发Cron任务
```bash
# 如果设置了CRON_SECRET
curl -H "Authorization: Bearer your-cron-secret" \
     https://your-app.vercel.app/api/cron/crawl-articles

# 如果没有设置CRON_SECRET
curl https://your-app.vercel.app/api/cron/crawl-articles
```

### 5. 常见问题

#### Q: 为什么定时任务显示停止状态？
A: 在Vercel环境下，内置定时任务会被自动禁用，改用Cron Jobs。这是正常现象。

#### Q: 如何确认Cron Jobs正在工作？
A: 
1. 检查Vercel控制台的Functions日志
2. 访问 `/api/status` 查看环境状态
3. 观察数据库中是否有新文章

#### Q: Cron任务执行失败怎么办？
A: 
1. 检查环境变量是否正确配置
2. 确认数据库连接正常
3. 查看Vercel Functions日志中的错误信息
4. 检查微信凭证是否有效

#### Q: 如何调整Cron执行频率？
A: 修改 `vercel.json` 中的 `schedule` 字段：
```json
{
  "crons": [
    {
      "path": "/api/cron/crawl-articles",
      "schedule": "*/5 * * * *"  // 改为每5分钟执行
    }
  ]
}
```

### 6. 性能优化

#### 函数超时设置
```json
{
  "functions": {
    "app/api/cron/crawl-articles/route.ts": {
      "maxDuration": 60
    }
  }
}
```

#### 批量处理限制
- 每次Cron执行最多处理3个公众号
- 避免单次执行时间过长导致超时
- 通过多次执行覆盖所有公众号

#### 冷却期机制
- 30分钟内发布过文章的公众号会被跳过
- 避免频繁请求微信API
- 提高系统稳定性

### 7. 本地开发

在本地开发时，内置定时任务仍然可以正常使用：

```bash
# 本地启动
npm run dev

# 检查状态
curl http://localhost:3000/api/status

# 手动初始化（如果需要）
curl -X POST http://localhost:3000/api/init
```

### 8. 数据库注意事项

#### 连接池配置
Vercel函数是无状态的，需要合理配置数据库连接：

```javascript
// prisma/schema.prisma
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  relationMode = "prisma"  // 如果使用PlanetScale
}
```

#### 连接数限制
- 使用连接池避免连接数过多
- 考虑使用PlanetScale等Serverless数据库
- 设置合理的连接超时时间

这样配置后，你的应用在Vercel上就能正常工作，定时任务会通过Cron Jobs自动执行，不再依赖长时间运行的后台进程。
