import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../lib/auth';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const accountId = searchParams.get('accountId');
    const cursor = searchParams.get('cursor'); // 用于游标分页
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search'); // 搜索关键词
    const startDate = searchParams.get('startDate'); // 开始日期
    const endDate = searchParams.get('endDate'); // 结束日期

    // 如果没有指定accountId，获取用户所有订阅的文章
    let whereCondition: any = {};

    if (accountId) {
      // 验证用户是否订阅了该公众号
      const subscription = await prisma.subscription.findFirst({
        where: {
          userId: user.id,
          wechatAccountId: accountId
        }
      });

      if (!subscription) {
        return NextResponse.json({ error: '未订阅该公众号' }, { status: 403 });
      }

      whereCondition.wechatAccountId = accountId;
    } else {
      // 获取用户所有订阅的公众号文章
      const subscriptions = await prisma.subscription.findMany({
        where: { userId: user.id },
        select: { wechatAccountId: true }
      });

      if (subscriptions.length === 0) {
        return NextResponse.json({
          articles: [],
          pagination: { hasMore: false, nextCursor: null }
        });
      }

      whereCondition.wechatAccountId = {
        in: subscriptions.map(sub => sub.wechatAccountId)
      };
    }

    // 添加搜索条件 (MySQL 不支持 mode: 'insensitive')
    if (search) {
      whereCondition.OR = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { author: { contains: search } }
      ];
    }

    // 添加时间范围条件
    const dateConditions: any = {};

    if (startDate) {
      dateConditions.gte = new Date(startDate);
    }

    if (endDate) {
      // 结束日期包含当天，所以设置为第二天的开始
      const endDateTime = new Date(endDate);
      endDateTime.setDate(endDateTime.getDate() + 1);
      dateConditions.lt = endDateTime;
    }

    // 游标分页条件
    if (cursor) {
      dateConditions.lt = new Date(cursor);
    }

    // 如果有日期条件，应用到publishDate字段
    if (Object.keys(dateConditions).length > 0) {
      whereCondition.publishDate = dateConditions;
    }

    const articles = await prisma.article.findMany({
      where: whereCondition,
      include: {
        wechatAccount: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: { publishDate: 'desc' },
      take: limit + 1 // 多取一条用于判断是否还有更多
    });

    const hasMore = articles.length > limit;
    const resultArticles = hasMore ? articles.slice(0, limit) : articles;
    const nextCursor = hasMore && resultArticles.length > 0
      ? resultArticles[resultArticles.length - 1].publishDate.toISOString()
      : null;

    return NextResponse.json({
      articles: resultArticles,
      pagination: {
        hasMore,
        nextCursor,
        total: resultArticles.length
      }
    });
  } catch (error) {
    console.error('获取文章列表失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}