# Webhook通知功能使用指南

## 功能概述

FeedWe支持通过Webhook向用户发送新文章通知，目前支持以下三种平台：

- 🏢 **企业微信** (WeWork)
- 📱 **钉钉** (DingTalk) 
- 🚀 **飞书** (Feishu)

当用户订阅的微信公众号发布新文章时，系统会自动向配置了Webhook的用户发送通知消息。

## 管理员配置

### 1. 访问用户管理页面

登录管理后台，进入 `用户管理` 页面：
```
http://your-domain.com/admin/users
```

### 2. 配置用户Webhook

1. 在用户列表中找到目标用户
2. 点击 `Webhook` 按钮
3. 在弹出的配置窗口中填写：
   - **Webhook URL**: 第三方平台提供的Webhook地址
   - **Webhook类型**: 选择企业微信/钉钉/飞书
   - **启用状态**: 勾选启用Webhook通知
4. 点击 `测试连接` 验证配置是否正确
5. 点击 `保存` 保存配置

## 各平台Webhook配置

### 企业微信 (WeWork)

1. 在企业微信管理后台创建群机器人
2. 获取Webhook URL，格式如下：
   ```
   https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY
   ```
3. 在FeedWe中选择 `企业微信` 类型

**消息格式**: Markdown格式，支持链接跳转

### 钉钉 (DingTalk)

1. 在钉钉群中添加自定义机器人
2. 获取Webhook URL，格式如下：
   ```
   https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN
   ```
3. 在FeedWe中选择 `钉钉` 类型

**消息格式**: Markdown格式，支持链接跳转

### 飞书 (Feishu)

1. 在飞书群中添加自定义机器人
2. 获取Webhook URL，格式如下：
   ```
   https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_HOOK_ID
   ```
3. 在FeedWe中选择 `飞书` 类型

**消息格式**: 富文本格式，支持链接跳转

## 通知消息格式

系统会根据文章类型发送不同格式的通知消息：

### 📰 主文章通知

主文章（每次推送的第一篇文章）会发送更醒目的通知：

**企业微信**: 使用图文消息（news）格式
- 包含封面图片
- 显示完整的文章摘要
- 可点击跳转到原文
- 更加美观和醒目

**钉钉/飞书**: 使用Markdown格式，包含完整信息

### 📄 副文章通知

副文章（推送中的其他文章）会合并在一条消息中发送：

**企业微信**: 使用文本消息格式
- 多篇副文章合并在一条消息中
- 显示文章总数和列表
- 每篇文章包含标题和链接
- 减少推送频率，避免过度打扰

**钉钉**: 使用Markdown格式，支持链接点击
**飞书**: 使用富文本格式，简洁展示

**合并规则**:
- 同一次推送的所有副文章会合并发送
- 如果只有1篇副文章，也会使用合并格式
- 最多支持显示10篇副文章（超出部分会省略）

### 通知内容包含

所有通知都包含以下基本信息：
- 🏢 **公众号**: 发布文章的微信公众号名称
- 📝 **文章标题**: 可点击跳转到原文
- ⏰ **发布时间**: 文章发布的时间
- 📄 **摘要**: 文章摘要（如果有）
- 🔗 **原文链接**: 直接访问微信文章

## API接口

### 获取用户Webhook配置
```http
GET /api/admin/users/{userId}/webhook
Authorization: Bearer {admin_token}
```

### 更新用户Webhook配置
```http
PATCH /api/admin/users/{userId}/webhook
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "webhookUrl": "https://example.com/webhook",
  "webhookType": "WEWORK",
  "webhookEnabled": true
}
```

### 测试Webhook连接
```http
POST /api/admin/users/{userId}/webhook
Authorization: Bearer {admin_token}
```

## 故障排除

### 1. 测试连接失败

- 检查Webhook URL是否正确
- 确认第三方平台的机器人权限
- 验证网络连接是否正常

### 2. 收不到通知

- 确认用户已启用Webhook通知
- 检查用户是否订阅了相关公众号
- 查看服务器日志是否有错误信息

### 3. 消息格式异常

- 不同平台支持的消息格式略有差异
- 确认选择了正确的Webhook类型
- 检查第三方平台的消息格式要求

## 开发测试

使用测试脚本验证Webhook功能：

```bash
node scripts/test-webhook-notifications.js
```

**注意**: 需要先在脚本中配置真实的Webhook URL。

## 安全建议

1. 定期更换Webhook URL中的密钥
2. 限制机器人的权限范围
3. 监控异常的Webhook调用
4. 设置合理的消息发送频率限制

## 技术实现

- **通知服务**: `lib/webhook-notification.ts`
- **定时任务**: `lib/wechat-crawler-scheduler.ts`
- **管理界面**: `app/admin/users/page.tsx`
- **API接口**: `app/api/admin/users/[id]/webhook/route.ts`
