const fetch = require('node-fetch');

async function testAPIMockLogin() {
  try {
    console.log('🎭 测试API模拟登录功能...');

    // 模拟管理员token（需要先登录获取真实token）
    const adminToken = 'your-admin-token-here'; // 这里需要替换为真实的管理员token
    
    const baseUrl = 'http://localhost:3001'; // 注意端口是3001
    
    console.log('\n=== 步骤1: 创建模拟登录会话 ===');
    let sessionId = '';
    
    try {
      const response = await fetch(`${baseUrl}/api/crawler/login`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      const data = await response.json();
      console.log('📊 响应状态:', response.status);
      
      if (response.ok) {
        console.log('✅ 登录会话创建成功');
        console.log('📋 会话ID:', data.sessionId);
        console.log('📱 二维码数据长度:', data.qrcode ? data.qrcode.length : 0);
        sessionId = data.sessionId;
        
        if (data.qrcode) {
          console.log('✅ 二维码数据获取成功');
        } else {
          console.log('❌ 未获取到二维码数据');
        }
      } else {
        console.log('❌ 创建登录会话失败:', data.error);
        return;
      }
    } catch (error) {
      console.error('❌ 请求失败:', error.message);
      return;
    }

    console.log('\n=== 步骤2: 监控登录状态变化 ===');
    console.log('💡 模拟模式时序:');
    console.log('  - 0秒: 生成二维码');
    console.log('  - 10秒: 模拟扫码');
    console.log('  - 20秒: 模拟确认登录');
    
    // 轮询检查登录状态
    let attempts = 0;
    const maxAttempts = 30; // 最多检查30次（60秒）
    
    const checkStatus = async () => {
      try {
        const response = await fetch(`${baseUrl}/api/crawler/login?sessionId=${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });
        
        const data = await response.json();
        
        if (response.ok && data.session) {
          const status = data.session.status;
          const elapsed = Math.floor((Date.now() - new Date(data.session.createdAt).getTime()) / 1000);
          
          console.log(`📊 [${elapsed}s] 状态: ${status}`);
          
          if (status === 'confirmed') {
            console.log('🎉 登录成功！');
            
            // 检查Cookie是否保存
            console.log('\n=== 步骤3: 检查Cookie保存 ===');
            const cookieResponse = await fetch(`${baseUrl}/api/crawler/cookies`, {
              headers: {
                'Authorization': `Bearer ${adminToken}`
              }
            });
            
            const cookieData = await cookieResponse.json();
            console.log('🍪 Cookie状态:', cookieData);
            
            if (cookieData.hasValidCookie) {
              console.log('✅ Cookie已成功保存到数据库');
              console.log('📅 登录时间:', cookieData.loginTime);
              console.log('🔗 Cookie预览:', cookieData.cookiePreview);
            } else {
              console.log('❌ Cookie未保存或无效');
            }
            
            console.log('\n🎉 模拟登录测试完成！');
            return true;
          } else if (status === 'failed' || status === 'expired') {
            console.log(`❌ 登录${status === 'failed' ? '失败' : '过期'}`);
            return false;
          }
        } else {
          console.log('❌ 获取状态失败:', data.error);
        }
        
        attempts++;
        if (attempts >= maxAttempts) {
          console.log('⏰ 超时，停止检查');
          return false;
        }
        
        // 等待2秒后继续检查
        setTimeout(checkStatus, 2000);
        
      } catch (error) {
        console.error('❌ 检查状态失败:', error.message);
        return false;
      }
    };
    
    // 开始检查状态
    await checkStatus();

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

console.log('🎭 API模拟登录测试');
console.log('💡 注意: 需要先登录管理后台获取有效的管理员token');
console.log('💡 然后将上面的 adminToken 替换为真实token');
console.log('💡 或者直接在浏览器中访问 http://localhost:3001/admin/crawler 进行测试');

testAPIMockLogin();
