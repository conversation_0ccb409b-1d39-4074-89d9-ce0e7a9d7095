import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../lib/auth';
import * as XLSX from 'xlsx';

const prisma = new PrismaClient();

// 支持GET方法（原有功能）和POST方法（按文章ID导出）
export async function GET(req: NextRequest) {
  return handleExport(req, 'GET');
}

export async function POST(req: NextRequest) {
  return handleExport(req, 'POST');
}

async function handleExport(req: NextRequest, method: 'GET' | 'POST') {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    let whereCondition: any = {};
    let accountId: string | null = null;

    if (method === 'POST') {
      // POST方法：按文章ID导出
      const body = await req.json();
      const { articleIds } = body;

      if (!articleIds || !Array.isArray(articleIds) || articleIds.length === 0) {
        return NextResponse.json({ error: '请选择要导出的文章' }, { status: 400 });
      }

      whereCondition = {
        id: { in: articleIds },
        subscription: {
          userId: user.id
        }
      };
    } else {
      // GET方法：原有的查询条件导出
      const { searchParams } = new URL(req.url);
      accountId = searchParams.get('accountId');
      const search = searchParams.get('search');
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');

      // 构建查询条件
      whereCondition = {};

      if (accountId) {
        // 验证用户是否订阅了该公众号
        const subscription = await prisma.subscription.findFirst({
          where: {
            userId: user.id,
            wechatAccountId: accountId
          }
        });

        if (!subscription) {
          return NextResponse.json({ error: '未订阅该公众号' }, { status: 403 });
        }

        whereCondition.wechatAccountId = accountId;
      } else {
        // 获取用户所有订阅的公众号文章
        const subscriptions = await prisma.subscription.findMany({
          where: { userId: user.id },
          select: { wechatAccountId: true }
        });

        if (subscriptions.length === 0) {
          return NextResponse.json({ error: '没有订阅的公众号' }, { status: 400 });
        }

        whereCondition.wechatAccountId = {
          in: subscriptions.map(sub => sub.wechatAccountId)
        };
      }

      // 添加搜索条件 (MySQL 不支持 mode: 'insensitive')
      if (search) {
        whereCondition.OR = [
          { title: { contains: search } },
          { summary: { contains: search } },
          { author: { contains: search } }
        ];
      }

      // 添加日期范围条件
      if (startDate || endDate) {
        whereCondition.publishDate = {};
        if (startDate) {
          whereCondition.publishDate.gte = new Date(startDate);
        }
        if (endDate) {
          whereCondition.publishDate.lte = new Date(endDate);
        }
      }
    }

    // 获取文章数据
    const articles = await prisma.article.findMany({
      where: whereCondition,
      include: {
        wechatAccount: {
          select: {
            name: true
          }
        }
      },
      orderBy: { publishDate: 'desc' },
      take: 10000 // 限制最大导出数量
    });

    if (articles.length === 0) {
      return NextResponse.json({ error: '没有找到符合条件的文章' }, { status: 400 });
    }

    // 准备Excel数据
    const excelData = articles.map((article, index) => ({
      '序号': index + 1,
      '文章标题': article.title,
      '公众号': article.wechatAccount.name,
      '作者': article.author || '',
      '发布时间': article.publishDate.toLocaleDateString('zh-CN'),
      '文章链接': article.url,
      '摘要': article.summary || '',
      '阅读数': article.readCount || 0,
      '点赞数': article.likeCount || 0,
      '标签': article.tags || '',
      '创建时间': article.createdAt.toLocaleDateString('zh-CN')
    }));

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // 设置列宽
    const columnWidths = [
      { wch: 8 },   // 序号
      { wch: 40 },  // 文章标题
      { wch: 20 },  // 公众号
      { wch: 15 },  // 作者
      { wch: 12 },  // 发布时间
      { wch: 50 },  // 文章链接
      { wch: 60 },  // 摘要
      { wch: 10 },  // 阅读数
      { wch: 10 },  // 点赞数
      { wch: 20 },  // 标签
      { wch: 12 }   // 创建时间
    ];
    worksheet['!cols'] = columnWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '文章列表');

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx' 
    });

    // 生成文件名
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const accountName = accountId ? 
      articles[0]?.wechatAccount.name || 'unknown' : 
      '全部订阅';
    const filename = `文章导出_${accountName}_${dateStr}.xlsx`;

    // 返回Excel文件
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`,
        'Content-Length': excelBuffer.length.toString()
      }
    });

  } catch (error) {
    console.error('导出Excel失败:', error);
    return NextResponse.json({ error: '导出失败，请稍后重试' }, { status: 500 });
  }
}
