#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 强制清理Prisma文件...');

try {
  // 停止所有可能的Node进程
  console.log('🛑 停止Node进程...');
  try {
    execSync('taskkill /f /im node.exe', { stdio: 'ignore' });
  } catch (e) {
    // 忽略错误，可能没有运行的Node进程
  }

  // 等待进程完全停止
  console.log('⏳ 等待进程停止...');
  setTimeout(() => {
    try {
      // 使用Windows命令强制删除
      console.log('🗑️ 删除.prisma目录...');
      const prismaPath = path.join(__dirname, '..', 'node_modules', '.prisma');
      if (fs.existsSync(prismaPath)) {
        execSync(`rmdir /s /q "${prismaPath}"`, { stdio: 'inherit' });
      }

      console.log('🗑️ 删除@prisma/client目录...');
      const clientPath = path.join(__dirname, '..', 'node_modules', '@prisma', 'client');
      if (fs.existsSync(clientPath)) {
        execSync(`rmdir /s /q "${clientPath}"`, { stdio: 'inherit' });
      }

      console.log('✅ 清理完成！');
      console.log('📦 现在运行: npm install && npx prisma generate');
    } catch (error) {
      console.error('❌ 清理失败:', error.message);
      console.log('💡 请手动删除以下目录:');
      console.log('   - node_modules\\.prisma');
      console.log('   - node_modules\\@prisma\\client');
      console.log('然后运行: npm install && npx prisma generate');
    }
  }, 2000);

} catch (error) {
  console.error('❌ 清理过程出错:', error.message);
}
