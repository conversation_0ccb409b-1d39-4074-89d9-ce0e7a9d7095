'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import Navigation from '../../../components/Navigation';
import { Article } from '../../../types/index';
import { DownloadButton } from '../../../../components/DownloadButton';

// 工具函数：检查是否为微信图片URL
const isWechatImageUrl = (url: string): boolean => {
  if (!url) return false;
  const wechatDomains = ['mmbiz.qpic.cn', 'mmbiz.qlogo.cn', 'wx.qlogo.cn', 'thirdwx.qlogo.cn'];
  return wechatDomains.some(domain => url.includes(domain));
};

// 工具函数：获取代理后的图片URL（仅用于显示）
const getDisplayImageUrl = (url: string): string => {
  if (!url) return url;
  // 如果已经是代理URL，直接返回
  if (url.startsWith('/api/proxy/wechat-image')) return url;
  // 如果不是微信图片URL，直接返回
  if (!isWechatImageUrl(url)) return url;
  return `/api/proxy/wechat-image?url=${encodeURIComponent(url)}`;
};

export default function ArticleDetailPage() {
  const params = useParams();
  const accountId = params.accountId as string;
  const articleId = params.articleId as string;
  
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (articleId) {
      fetchArticle();
    }
  }, [articleId]);

  const fetchArticle = async () => {
    try {
      const res = await fetch(`/api/articles/${articleId}`);
      if (!res.ok) {
        throw new Error('文章不存在');
      }
      const data = await res.json();
      setArticle(data);
    } catch (error) {
      setError(error instanceof Error ? error.message : '获取文章失败');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      weekday: 'long'
    });
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto py-10">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="max-w-4xl mx-auto py-10 px-4">
        <div className="text-center">
          <div className="text-red-500 text-lg mb-4">{error || '文章不存在'}</div>
          <Link 
            href={`/articles/${accountId}`}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            返回文章列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <Navigation
        currentPage="article"
        customActions={
          article && (
            <div className="flex items-center space-x-2">
              <DownloadButton
                articleId={article.id}
                articleTitle={article.title}
                variant="outline"
                size="sm"
              />
              <a
                href={article.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors shadow-sm hover:shadow-md"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-1M14 6h6m0 0v6m0-6L10 16" />
                </svg>
                阅读原文
              </a>
            </div>
          )
        }
      />

      <div className="max-w-4xl mx-auto py-10 px-4">

      {/* 文章内容 */}
      <article className="bg-white rounded-lg shadow-md">
        {/* 文章头部 */}
        <div className="p-8 border-b border-gray-200">
          <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">
            {article.title}
          </h1>
          
          <div className="flex items-center">
            <img
              src={getDisplayImageUrl(article.wechatAccount.avatar)}
              alt={article.wechatAccount.name}
              className="w-10 h-10 rounded-full mr-3"
            />
            <div>
              <div className="font-medium text-gray-900">
                {article.wechatAccount.name}
              </div>
              <div className="text-sm text-gray-500">
                发布时间：{formatDate(article.publishDate)}
              </div>
            </div>
          </div>
        </div>

        {/* 封面图片 */}
        {article.coverImage && (
          <div className="px-8 pt-8">
            <img
              src={getDisplayImageUrl(article.coverImage)}
              alt={article.title}
              className="w-full h-auto rounded-lg"
            />
          </div>
        )}

        {/* 文章摘要 */}
        {article.summary && (
          <div className="px-8 pt-8">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">文章摘要</h3>
              <p className="text-gray-700 leading-relaxed">{article.summary}</p>
            </div>
          </div>
        )}

        {/* 文章元信息 */}
        {(article.author || article.tags) && (
          <div className="px-8 pt-4">
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              {article.author && (
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  作者：{article.author}
                </span>
              )}
              {article.tags && (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  {article.tags.split(',').filter(tag => tag.trim()).map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {tag.trim()}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 阅读原文区域 */}
        <div className="p-8">
          <div className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8">
            <div className="mb-6">
              <svg className="w-16 h-16 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">阅读完整文章</h3>
              <p className="text-gray-600">
                点击下方按钮跳转到原文页面阅读完整内容
              </p>
            </div>

            <a
              href={article.url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg transition-colors text-lg font-medium shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-1M14 6h6m0 0v6m0-6L10 16" />
              </svg>
              阅读原文
            </a>

            <p className="text-xs text-gray-500 mt-4">
              将在新窗口中打开微信公众号原文
            </p>
          </div>
        </div>
      </article>
      </div>
    </div>
  );
}
