import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { WechatCrawlerScheduler } from '../../../../../lib/wechat-crawler-scheduler';

// 获取定时任务状态
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const scheduler = WechatCrawlerScheduler.getInstance();
    const status = scheduler.getStatus();

    return NextResponse.json({
      success: true,
      ...status
    });
  } catch (error) {
    console.error('获取定时任务状态失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取状态失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 启动定时任务或重置失败计数
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);

    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const body = await request.json().catch(() => ({}));
    const action = body.action || 'start';

    const scheduler = WechatCrawlerScheduler.getInstance();

    if (action === 'reset') {
      scheduler.resetFailureCount();
      return NextResponse.json({
        success: true,
        message: '失败计数器已重置'
      });
    } else {
      scheduler.start();
      return NextResponse.json({
        success: true,
        message: '定时任务已启动'
      });
    }
  } catch (error) {
    console.error('操作定时任务失败:', error);
    const errorMessage = error instanceof Error ? error.message : '操作失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 停止定时任务
export async function DELETE(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const scheduler = WechatCrawlerScheduler.getInstance();
    scheduler.stop();

    return NextResponse.json({
      success: true,
      message: '定时任务已停止'
    });
  } catch (error) {
    console.error('停止定时任务失败:', error);
    const errorMessage = error instanceof Error ? error.message : '停止失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
