const { execSync } = require('child_process');

async function runMigration() {
  try {
    console.log('🔄 Starting database migration...');
    
    // 设置超时时间为30秒
    const options = { 
      stdio: 'inherit', 
      timeout: 30000,
      env: { ...process.env }
    };
    
    // 运行迁移
    execSync('npx prisma migrate deploy', options);
    
    console.log('✅ Migration completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    
    // 如果迁移失败，尝试推送schema
    try {
      console.log('🔄 Trying to push schema instead...');
      execSync('npx prisma db push --force-reset', options);
      console.log('✅ Schema push completed');
    } catch (pushError) {
      console.error('❌ Schema push also failed:', pushError.message);
      process.exit(1);
    }
  }
}

runMigration();
