import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../../lib/auth/admin';
import { prisma } from '../../../../../../lib/prisma';
import { WebhookType } from '@prisma/client';
import { WebhookNotificationService } from '../../../../../../lib/webhook-notification';

// 获取用户webhook配置
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.USER_MANAGEMENT)(user);

    const params = await context.params;
    const userId = params.id;

    // 获取用户webhook配置
    const userWebhook = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        webhookUrl: true,
        webhookType: true,
        webhookEnabled: true
      }
    });

    if (!userWebhook) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      webhook: userWebhook
    });
  } catch (error) {
    console.error('获取用户webhook配置失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取webhook配置失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 更新用户webhook配置
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.USER_MANAGEMENT)(user);

    const { webhookUrl, webhookType, webhookEnabled } = await request.json();
    const params = await context.params;
    const userId = params.id;

    // 验证webhook类型
    if (webhookType && !Object.values(WebhookType).includes(webhookType)) {
      return NextResponse.json(
        { error: '无效的webhook类型' },
        { status: 400 }
      );
    }

    // 验证webhook URL格式
    if (webhookUrl && webhookUrl.trim()) {
      try {
        new URL(webhookUrl);
      } catch {
        return NextResponse.json(
          { error: '无效的webhook URL格式' },
          { status: 400 }
        );
      }
    }

    // 更新用户webhook配置
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        webhookUrl: webhookUrl || null,
        webhookType: webhookType || null,
        webhookEnabled: Boolean(webhookEnabled)
      },
      select: {
        id: true,
        email: true,
        name: true,
        webhookUrl: true,
        webhookType: true,
        webhookEnabled: true
      }
    });

    return NextResponse.json({
      success: true,
      webhook: updatedUser,
      message: 'Webhook配置更新成功'
    });
  } catch (error) {
    console.error('更新用户webhook配置失败:', error);
    const errorMessage = error instanceof Error ? error.message : '更新webhook配置失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 测试用户webhook连接
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.USER_MANAGEMENT)(user);

    const params = await context.params;
    const userId = params.id;

    // 获取用户webhook配置
    const userWebhook = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        webhookUrl: true,
        webhookType: true,
        webhookEnabled: true
      }
    });

    if (!userWebhook) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    if (!userWebhook.webhookUrl || !userWebhook.webhookType) {
      return NextResponse.json(
        { error: 'Webhook未配置' },
        { status: 400 }
      );
    }

    // 测试webhook连接
    const success = await WebhookNotificationService.testWebhookConnection(
      userWebhook.webhookUrl,
      userWebhook.webhookType
    );

    return NextResponse.json({
      success,
      message: success ? 'Webhook测试成功' : 'Webhook测试失败'
    });
  } catch (error) {
    console.error('测试webhook连接失败:', error);
    const errorMessage = error instanceof Error ? error.message : '测试webhook连接失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
