import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface JWTPayload {
  userId: string;
  email: string;
  name?: string;
  role?: string;
  iat?: number;
  exp?: number;
}

export function signToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '7d', // Token expires in 7 days
  });
}

export function verifyToken(token: string): JWTPayload {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    return decoded;
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('无效的token');
    } else if (error instanceof jwt.TokenExpiredError) {
      throw new Error('token已过期');
    } else {
      throw new Error('token验证失败');
    }
  }
}

export function refreshToken(token: string): string {
  try {
    const decoded = verifyToken(token);
    // Remove iat and exp from the payload before creating new token
    const { iat, exp, ...payload } = decoded;
    return signToken(payload);
  } catch (error) {
    throw new Error('无法刷新token');
  }
}

// 从请求头中提取token
export function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// 验证并返回用户信息
export function authenticateRequest(authHeader: string | null): JWTPayload {
  const token = extractTokenFromHeader(authHeader);
  if (!token) {
    throw new Error('未提供认证token');
  }
  return verifyToken(token);
}
