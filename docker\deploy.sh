#!/bin/bash

# FeedWe 定时任务爬虫服务部署脚本

set -e

echo "🚀 开始部署 FeedWe 定时任务爬虫服务..."

# 检查 Docker 和 Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env.crawler" ]; then
    echo "❌ 环境变量文件 .env.crawler 不存在"
    echo "请复制 .env.crawler.example 为 .env.crawler 并填写配置"
    exit 1
fi

# 加载环境变量
export $(cat .env.crawler | grep -v '^#' | xargs)

# 检查必要的环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "❌ 错误: DATABASE_URL 环境变量未设置"
    exit 1
fi

echo "✅ 环境检查通过"

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.crawler.yml down || true

# 检查是否使用预构建镜像
if grep -q "image:" docker-compose.crawler.yml && ! grep -q "build:" docker-compose.crawler.yml; then
    echo "📦 使用预构建镜像启动服务..."
    docker-compose -f docker-compose.crawler.yml pull
    docker-compose -f docker-compose.crawler.yml up -d
else
    echo "🔨 本地构建并启动服务..."
    docker-compose -f docker-compose.crawler.yml up -d --build
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 健康检查: http://localhost:3001/health"
    echo "📊 状态查询: http://localhost:3001/status"
else
    echo "❌ 服务启动失败，请检查日志:"
    docker-compose -f docker-compose.crawler.yml logs
    exit 1
fi

echo "🎉 部署完成！"
