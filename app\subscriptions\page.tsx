'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Navigation from '../components/Navigation';
import AddSubscriptionModal from '../components/AddSubscriptionModal';
import { useAuth, withAuth } from '../components/AuthProvider';

type WechatAccount = {
  id: string;
  name: string;
  avatar: string;
  description?: string;
  openid?: string;
};

type Subscription = {
  id: string;
  createdAt: string;
  wechatAccount: WechatAccount;
};

function SubscriptionsPage() {
  const { token } = useAuth();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [unsubscribing, setUnsubscribing] = useState<string | null>(null);
  const [message, setMessage] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  // 获取用户订阅列表
  useEffect(() => {
    fetchSubscriptions();
  }, []);

  const fetchSubscriptions = async () => {
    if (!token) return;

    try {
      const res = await fetch('/api/subscriptions', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await res.json();
      setSubscriptions(data);
    } catch (error) {
      console.error('获取订阅列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 取消订阅
  const handleUnsubscribe = async (subscriptionId: string) => {
    if (!token) return;

    setUnsubscribing(subscriptionId);
    setMessage('');

    try {
      const res = await fetch(`/api/subscriptions/${subscriptionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (res.ok) {
        setMessage('取消订阅成功！');
        setSubscriptions(prev => prev.filter(sub => sub.id !== subscriptionId));
      } else {
        setMessage('取消订阅失败');
      }
    } catch (error) {
      setMessage('取消订阅失败');
    } finally {
      setUnsubscribing(null);
    }
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto py-10">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <Navigation currentPage="subscriptions" />

      <div className="max-w-4xl mx-auto py-10 px-4">
        <div className="mb-8 flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">我的订阅</h1>
            <p className="text-gray-600 mt-2">管理您订阅的微信公众号</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors shadow-sm hover:shadow-md"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            添加订阅
          </button>
        </div>

      {message && (
        <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
          {message}
        </div>
      )}

      {subscriptions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-4">您还没有订阅任何公众号</div>
          <div className="space-x-4">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
            >
              立即添加订阅
            </button>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {subscriptions.map((subscription) => (
            <div
              key={subscription.id}
              className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow flex flex-col h-full"
            >
              {/* 头部信息 */}
              <div className="flex items-center mb-4">
                <img
                  src={subscription.wechatAccount.avatar}
                  alt={subscription.wechatAccount.name}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div className="flex-1">
                  <h3 className="font-semibold text-lg text-gray-900">
                    {subscription.wechatAccount.name}
                  </h3>
                  <p className="text-sm text-gray-500">
                    订阅于 {new Date(subscription.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {/* 描述内容 - 使用flex-1占据剩余空间 */}
              <div className="flex-1 mb-4">
                {subscription.wechatAccount.description ? (
                  <p className="text-gray-600 text-sm line-clamp-3">
                    {subscription.wechatAccount.description}
                  </p>
                ) : (
                  <p className="text-gray-400 text-sm italic">暂无描述</p>
                )}
              </div>

              {/* 按钮区域 - 固定在底部 */}
              <div className="flex space-x-2 mt-auto">
                <Link
                  href={`/feed?accountId=${subscription.wechatAccount.id}`}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-center py-2 px-4 rounded transition-colors font-medium"
                >
                  查看文章
                </Link>
                <button
                  onClick={() => handleUnsubscribe(subscription.id)}
                  disabled={unsubscribing === subscription.id}
                  className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded transition-colors disabled:opacity-50 whitespace-nowrap"
                >
                  {unsubscribing === subscription.id ? '取消中...' : '取消订阅'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
      </div>

      {/* 添加订阅弹窗 */}
      <AddSubscriptionModal
        isOpen={showAddModal}
        onClose={() => {
          setShowAddModal(false);
          // 刷新订阅列表
          fetchSubscriptions();
        }}
      />
    </div>
  );
}

export default withAuth(SubscriptionsPage);
