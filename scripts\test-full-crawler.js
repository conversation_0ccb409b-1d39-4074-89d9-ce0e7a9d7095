const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login.ts');
const { WeWorkWebhook } = require('../lib/wework-webhook.ts');
const { WechatCrawler } = require('../lib/wechat-crawler.ts');

async function testFullCrawlerFlow() {
  try {
    console.log('🚀 测试完整的微信爬虫流程...');

    // 检查企业微信配置
    const webhookUrl = process.env.WEWORK_WEBHOOK_URL;
    let webhook = null;
    
    if (webhookUrl && !webhookUrl.includes('YOUR_WEBHOOK_KEY')) {
      webhook = new WeWorkWebhook(webhookUrl);
      console.log('📱 企业微信Webhook已配置');
    } else {
      console.log('⚠️ 企业微信Webhook未配置，将只在控制台显示结果');
    }

    let loginSuccess = false;
    let cookieString = '';
    let token = '';

    // 步骤1: 微信登录
    console.log('\n=== 步骤1: 微信登录 ===');
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
        
        if (webhook) {
          const session = wechatLogin.getSession();
          await webhook.sendWechatLoginQR(qrcode, session.id);
          console.log('✅ 二维码已发送到企业微信');
        } else {
          console.log('💡 请在企业微信中查看二维码');
        }
      },
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
        
        if (webhook) {
          const session = wechatLogin.getSession();
          await webhook.sendLoginStatusUpdate(session.id, status, data);
        }
      },
      onSuccess: async (userInfo, loginToken) => {
        console.log('🎉 登录成功！');
        console.log('👤 用户信息:', userInfo);
        
        loginSuccess = true;
        token = loginToken;
        cookieString = wechatLogin.getCookieString();
        
        console.log('🍪 Cookie字符串:', cookieString.substring(0, 100) + '...');
        
        // 步骤2: 测试文章爬取
        setTimeout(async () => {
          await testArticleCrawling(cookieString, token, webhook);
        }, 2000);
      },
      onError: async (error) => {
        console.error('❌ 登录失败:', error.message);
        process.exit(1);
      }
    });

    // 开始登录流程
    const sessionId = await wechatLogin.startLogin();
    console.log('📋 登录会话ID:', sessionId);

    // 监听登录完成或失败
    wechatLogin.on('expired', () => {
      console.log('⏰ 登录会话过期！');
      process.exit(1);
    });

    // 设置超时
    setTimeout(() => {
      if (!loginSuccess) {
        console.log('⏰ 测试超时，取消登录');
        wechatLogin.cancel();
        process.exit(1);
      }
    }, 5 * 60 * 1000); // 5分钟超时

    console.log('⏳ 等待扫码登录...');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 测试文章爬取
async function testArticleCrawling(cookieString, token, webhook) {
  try {
    console.log('\n=== 步骤2: 测试文章爬取 ===');
    
    if (!cookieString || !token) {
      console.log('❌ 缺少登录凭证，跳过文章爬取测试');
      return;
    }

    // 创建爬虫实例
    const crawler = new WechatCrawler({
      cookieString: cookieString,
      token: token,
      onProgress: (progress) => {
        console.log(`📊 爬取进度: ${progress.current}/${progress.total} - ${progress.article.title}`);
      },
      onComplete: async (articles) => {
        console.log(`✅ 爬取完成！共获取 ${articles.length} 篇文章`);
        
        // 显示文章列表
        articles.forEach((article, index) => {
          console.log(`${index + 1}. ${article.title}`);
          console.log(`   发布时间: ${new Date(article.publishTime).toLocaleString()}`);
          console.log(`   链接: ${article.url}`);
          console.log(`   摘要: ${article.digest.substring(0, 50)}...`);
          console.log('');
        });

        // 发送完成通知
        if (webhook) {
          await webhook.sendCrawlerNotification('success', {
            accountName: '测试公众号',
            taskId: 'test_task_' + Date.now(),
            articleCount: articles.length
          });
        }

        console.log('🎊 完整流程测试完成！');
        process.exit(0);
      },
      onError: async (error) => {
        console.error('❌ 爬取失败:', error.message);
        
        if (webhook) {
          await webhook.sendCrawlerNotification('error', {
            accountName: '测试公众号',
            taskId: 'test_task_' + Date.now(),
            error: error.message
          });
        }
        
        process.exit(1);
      }
    });

    // 开始爬取（使用模拟的fakeId）
    console.log('🕷️ 开始爬取文章...');
    await crawler.crawlArticles('mock_fake_id_123', '测试公众号', 5);

  } catch (error) {
    console.error('❌ 文章爬取测试失败:', error.message);
    process.exit(1);
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试被中断');
  process.exit(0);
});

testFullCrawlerFlow();
