const { WechatCrawlerLogin } = require('../lib/wechat-crawler-login.ts');
const { WeWorkWebhook } = require('../lib/wework-webhook.ts');
// WechatCrawler 已被整合到 WechatCrawlerService 中，不再单独使用

async function testFullCrawlerFlow() {
  try {
    console.log('🚀 测试完整的微信爬虫流程...');

    // 检查企业微信配置
    const webhookUrl = process.env.WEWORK_WEBHOOK_URL;
    let webhook = null;
    
    if (webhookUrl && !webhookUrl.includes('YOUR_WEBHOOK_KEY')) {
      webhook = new WeWorkWebhook(webhookUrl);
      console.log('📱 企业微信Webhook已配置');
    } else {
      console.log('⚠️ 企业微信Webhook未配置，将只在控制台显示结果');
    }

    let loginSuccess = false;
    let cookieString = '';
    let token = '';

    // 步骤1: 微信登录
    console.log('\n=== 步骤1: 微信登录 ===');
    const wechatLogin = new WechatCrawlerLogin({
      onQRCode: async (qrcode) => {
        console.log('📱 二维码已生成');
        
        if (webhook) {
          const session = wechatLogin.getSession();
          await webhook.sendWechatLoginQR(qrcode, session.id);
          console.log('✅ 二维码已发送到企业微信');
        } else {
          console.log('💡 请在企业微信中查看二维码');
        }
      },
      onStatusChange: async (status, data) => {
        console.log(`📊 登录状态: ${status}`);
        
        if (webhook) {
          const session = wechatLogin.getSession();
          await webhook.sendLoginStatusUpdate(session.id, status, data);
        }
      },
      onSuccess: async (userInfo, loginToken) => {
        console.log('🎉 登录成功！');
        console.log('👤 用户信息:', userInfo);
        
        loginSuccess = true;
        token = loginToken;
        cookieString = wechatLogin.getCookieString();
        
        console.log('🍪 Cookie字符串:', cookieString.substring(0, 100) + '...');
        
        // 步骤2: 测试文章爬取
        setTimeout(async () => {
          await testArticleCrawling(cookieString, token, webhook);
        }, 2000);
      },
      onError: async (error) => {
        console.error('❌ 登录失败:', error.message);
        process.exit(1);
      }
    });

    // 开始登录流程
    const sessionId = await wechatLogin.startLogin();
    console.log('📋 登录会话ID:', sessionId);

    // 监听登录完成或失败
    wechatLogin.on('expired', () => {
      console.log('⏰ 登录会话过期！');
      process.exit(1);
    });

    // 设置超时
    setTimeout(() => {
      if (!loginSuccess) {
        console.log('⏰ 测试超时，取消登录');
        wechatLogin.cancel();
        process.exit(1);
      }
    }, 5 * 60 * 1000); // 5分钟超时

    console.log('⏳ 等待扫码登录...');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 测试说明：爬取功能现在由定时任务自动执行
async function testArticleCrawling(cookieString, token, webhook) {
  try {
    console.log('\n=== 步骤2: 爬取功能说明 ===');

    if (!cookieString || !token) {
      console.log('❌ 缺少登录凭证，定时爬取任务无法执行');
      return;
    }

    console.log('✅ 登录成功！Cookie和Token已保存到数据库');
    console.log('💡 爬取功能现在由定时任务自动执行，无需手动触发');
    console.log('📋 您可以通过以下方式查看爬取状态：');
    console.log('   - 访问管理后台的爬虫管理页面');
    console.log('   - 查看定时任务的执行日志');
    console.log('   - 检查数据库中的文章数据');
    console.log('   - 查看应用状态接口: /api/status');

    // 发送完成通知
    if (webhook) {
      await webhook.sendCrawlerNotification('info', {
        message: '微信登录成功，定时爬取任务将自动执行',
        cookieStatus: 'saved',
        tokenStatus: 'saved'
      });
    }

    console.log('🎊 登录流程测试完成！');
    console.log('💡 定时爬取任务将在后台自动运行');
    process.exit(0);
    console.log('🕷️ 开始爬取文章...');
    await crawler.crawlArticles('mock_fake_id_123', '测试公众号', 5);

  } catch (error) {
    console.error('❌ 文章爬取测试失败:', error.message);
    process.exit(1);
  }
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 测试被中断');
  process.exit(0);
});

testFullCrawlerFlow();
