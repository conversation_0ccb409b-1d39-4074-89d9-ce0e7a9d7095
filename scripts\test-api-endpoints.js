const fetch = require('node-fetch');

async function testAPIEndpoints() {
  try {
    console.log('🔍 测试API端点连接...');

    const baseUrl = 'http://localhost:3001';
    
    // 测试基本连接
    console.log('\n=== 测试1: 基本连接 ===');
    try {
      const response = await fetch(`${baseUrl}/api/health`);
      console.log('健康检查响应状态:', response.status);
    } catch (error) {
      console.log('健康检查失败:', error.message);
    }

    // 测试登录API（无认证）
    console.log('\n=== 测试2: 登录API（无认证） ===');
    try {
      const response = await fetch(`${baseUrl}/api/crawler/login`);
      const data = await response.json();
      console.log('登录API响应状态:', response.status);
      console.log('登录API响应数据:', data);
    } catch (error) {
      console.log('登录API测试失败:', error.message);
    }

    // 测试Cookie API（无认证）
    console.log('\n=== 测试3: Cookie API（无认证） ===');
    try {
      const response = await fetch(`${baseUrl}/api/crawler/cookies`);
      const data = await response.json();
      console.log('Cookie API响应状态:', response.status);
      console.log('Cookie API响应数据:', data);
    } catch (error) {
      console.log('Cookie API测试失败:', error.message);
    }

    // 测试管理员登录
    console.log('\n=== 测试4: 管理员登录 ===');
    try {
      const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123456'
        })
      });
      
      const loginData = await loginResponse.json();
      console.log('管理员登录响应状态:', loginResponse.status);
      
      if (loginResponse.ok && loginData.token) {
        console.log('✅ 管理员登录成功');
        const adminToken = loginData.token;
        
        // 使用管理员token测试API
        console.log('\n=== 测试5: 使用管理员token测试API ===');
        
        // 测试登录API（有认证）
        try {
          const response = await fetch(`${baseUrl}/api/crawler/login`, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          const data = await response.json();
          console.log('认证登录API响应状态:', response.status);
          console.log('认证登录API响应数据:', data);
        } catch (error) {
          console.log('认证登录API测试失败:', error.message);
        }

        // 测试Cookie API（有认证）
        try {
          const response = await fetch(`${baseUrl}/api/crawler/cookies`, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          const data = await response.json();
          console.log('认证Cookie API响应状态:', response.status);
          console.log('认证Cookie API响应数据:', data);
        } catch (error) {
          console.log('认证Cookie API测试失败:', error.message);
        }

        // 测试创建登录会话
        console.log('\n=== 测试6: 创建登录会话 ===');
        try {
          const response = await fetch(`${baseUrl}/api/crawler/login`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          const data = await response.json();
          console.log('创建会话响应状态:', response.status);
          console.log('创建会话响应数据:', data);
          
          if (response.ok && data.sessionId) {
            console.log('✅ 登录会话创建成功');
            console.log('📋 会话ID:', data.sessionId);
            console.log('📱 二维码数据:', data.qrcode ? '已获取' : '未获取');
          }
        } catch (error) {
          console.log('创建会话测试失败:', error.message);
        }
        
      } else {
        console.log('❌ 管理员登录失败:', loginData.error);
      }
    } catch (error) {
      console.log('管理员登录测试失败:', error.message);
    }

    console.log('\n🎉 API端点测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试服务器连接
async function testServerConnection() {
  console.log('🔗 测试服务器连接...');
  
  const ports = [3000, 3001];
  
  for (const port of ports) {
    try {
      const response = await fetch(`http://localhost:${port}`, {
        timeout: 5000
      });
      console.log(`✅ 端口 ${port}: 服务器正在运行 (状态: ${response.status})`);
    } catch (error) {
      console.log(`❌ 端口 ${port}: 服务器未运行或无法连接`);
    }
  }
}

// 运行测试
async function runTests() {
  await testServerConnection();
  console.log('\n' + '='.repeat(50));
  await testAPIEndpoints();
}

runTests();
