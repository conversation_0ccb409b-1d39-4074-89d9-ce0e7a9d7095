#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🚀 Vercel构建开始...');

try {
  const options = { stdio: 'inherit' };

  console.log('📦 生成Prisma客户端...');
  execSync('npx prisma generate', options);

  console.log('🗄️ 运行数据库迁移...');
  // 优先使用DIRECT_URL，然后是POSTGRES_URL_NON_POOLING
  const migrationUrl = process.env.DIRECT_URL || process.env.POSTGRES_URL_NON_POOLING;
  
  if (migrationUrl) {
    console.log('📡 使用直连URL进行迁移...');
    execSync('npx prisma migrate deploy', {
      ...options,
      env: {
        ...process.env,
        DATABASE_URL: migrationUrl
      }
    });
  } else {
    console.log('📡 使用默认URL进行迁移...');
    execSync('npx prisma migrate deploy', options);
  }

  console.log('🏗️ 构建Next.js应用...');
  execSync('next build', options);

  console.log('✅ 构建完成！');
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  
  // 如果迁移失败，尝试只构建应用
  if (error.message.includes('migrate')) {
    console.log('⚠️ 迁移失败，尝试跳过迁移继续构建...');
    try {
      execSync('npx prisma generate', { stdio: 'inherit' });
      execSync('next build', { stdio: 'inherit' });
      console.log('✅ 构建完成（跳过迁移）');
    } catch (buildError) {
      console.error('❌ 构建也失败了:', buildError.message);
      process.exit(1);
    }
  } else {
    process.exit(1);
  }
}
