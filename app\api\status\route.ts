import { NextResponse } from 'next/server';
import { WechatCrawlerScheduler } from '../../../lib/wechat-crawler-scheduler';

export async function GET() {
  try {
    const isVercel = process.env.VERCEL === '1';
    const isProduction = process.env.NODE_ENV === 'production';
    const schedulerDisabled = process.env.DISABLE_CRAWLER_SCHEDULER === 'true';
    
    let schedulerStatus = null;
    
    if (!schedulerDisabled) {
      try {
        const scheduler = WechatCrawlerScheduler.getInstance();
        schedulerStatus = scheduler.getStatus();
      } catch (error) {
        schedulerStatus = {
          error: '无法获取定时任务状态',
          details: error instanceof Error ? error.message : '未知错误'
        };
      }
    }

    return NextResponse.json({
      success: true,
      environment: {
        isVercel,
        isProduction,
        nodeEnv: process.env.NODE_ENV,
        platform: process.platform,
        vercelRegion: process.env.VERCEL_REGION || 'unknown',
        vercelUrl: process.env.VERCEL_URL || 'unknown'
      },
      scheduler: {
        disabled: schedulerDisabled,
        status: schedulerStatus,
        recommendation: isVercel 
          ? 'Vercel环境建议使用Cron Jobs代替定时任务'
          : '可以使用内置定时任务'
      },
      cronJobs: {
        available: isVercel,
        endpoint: '/api/cron/crawl-articles',
        schedule: '每2分钟执行一次',
        note: isVercel 
          ? 'Vercel Cron Jobs已配置，会自动执行'
          : 'Cron Jobs仅在Vercel环境可用'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '获取状态失败',
      details: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
