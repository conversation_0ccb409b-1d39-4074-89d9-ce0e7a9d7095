/**
 * 测试自动初始化功能
 */

// 由于这是CommonJS脚本，我们需要动态导入ES模块
async function loadModules() {
  try {
    const autoInitModule = await import('../lib/auto-init.js');
    const schedulerModule = await import('../lib/wechat-crawler-scheduler.js');

    return {
      autoInitialize: autoInitModule.autoInitialize,
      forceReinitialize: autoInitModule.forceReinitialize,
      WechatCrawlerScheduler: schedulerModule.WechatCrawlerScheduler
    };
  } catch (error) {
    console.error('❌ 无法加载模块:', error.message);
    console.log('💡 请确保在项目根目录运行此脚本');
    process.exit(1);
  }
}

async function testAutoInit() {
  console.log('🧪 测试自动初始化功能...\n');

  try {
    // 加载模块
    const { autoInitialize, forceReinitialize, WechatCrawlerScheduler } = await loadModules();

    // 1. 测试自动初始化
    console.log('1️⃣ 测试自动初始化...');
    await autoInitialize();

    // 2. 检查定时任务状态
    console.log('\n2️⃣ 检查定时任务状态...');
    const scheduler = WechatCrawlerScheduler.getInstance();
    const status = scheduler.getStatus();
    
    console.log('📊 定时任务状态:');
    console.log(`  - 运行中: ${status.isRunning ? '✅' : '❌'}`);
    console.log(`  - 连续失败次数: ${status.consecutiveFailures}/${status.maxConsecutiveFailures}`);
    console.log(`  - 在允许时间内: ${status.isInAllowedTime ? '✅' : '❌'}`);
    
    if (status.nextExecution) {
      console.log(`  - 下次执行: ${new Date(status.nextExecution).toLocaleString()}`);
    }

    // 3. 测试强制重新初始化
    console.log('\n3️⃣ 测试强制重新初始化...');
    await forceReinitialize();
    
    const newStatus = scheduler.getStatus();
    console.log('📊 重新初始化后状态:');
    console.log(`  - 运行中: ${newStatus.isRunning ? '✅' : '❌'}`);
    
    // 4. 测试结果
    console.log('\n🏁 测试结果:');
    if (status.isRunning && newStatus.isRunning) {
      console.log('✅ 自动初始化功能正常工作');
      console.log('✅ 定时任务已成功启动');
    } else {
      console.log('❌ 自动初始化可能存在问题');
      console.log('💡 请检查环境变量 DISABLE_CRAWLER_SCHEDULER 是否设置为 true');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 测试API端点
async function testInitAPI() {
  console.log('\n🧪 测试初始化API端点...\n');

  const baseUrl = process.env.APP_URL || 'http://localhost:3000';
  
  try {
    // 测试GET请求
    console.log('1️⃣ 测试GET /api/init...');
    const getResponse = await fetch(`${baseUrl}/api/init`);
    const getData = await getResponse.json();
    
    console.log('📊 GET响应:');
    console.log(JSON.stringify(getData, null, 2));
    
    // 测试POST请求
    console.log('\n2️⃣ 测试POST /api/init...');
    const postResponse = await fetch(`${baseUrl}/api/init`, {
      method: 'POST'
    });
    const postData = await postResponse.json();
    
    console.log('📊 POST响应:');
    console.log(JSON.stringify(postData, null, 2));
    
    // 检查结果
    if (getData.success && postData.success) {
      console.log('\n✅ API端点测试通过');
    } else {
      console.log('\n❌ API端点测试失败');
    }

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    console.log('💡 请确保应用正在运行在', baseUrl);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--api-only')) {
    await testInitAPI();
  } else if (args.includes('--local-only')) {
    await testAutoInit();
  } else {
    await testAutoInit();
    await testInitAPI();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testAutoInit, testInitAPI };
