const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedAccounts() {
  try {
    console.log('🌱 开始添加测试公众号...');

    const accounts = [
      {
        name: '科技前沿',
        avatar: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=100&h=100&fit=crop&crop=face',
        description: '关注最新科技动态，分享前沿技术资讯',
        openid: 'tech_frontier_001'
      },
      {
        name: '财经观察',
        avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        description: '专业财经分析，投资理财指导',
        openid: 'finance_watch_002'
      },
      {
        name: '生活百科',
        avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=100&h=100&fit=crop&crop=face',
        description: '生活小贴士，健康养生知识分享',
        openid: 'life_tips_003'
      },
      {
        name: '教育资讯',
        avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
        description: '教育政策解读，学习方法分享',
        openid: 'education_news_004'
      },
      {
        name: '旅游攻略',
        avatar: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
        description: '精选旅游目的地，实用出行攻略',
        openid: 'travel_guide_005'
      }
    ];

    for (const account of accounts) {
      // 检查是否已存在
      const existing = await prisma.wechatAccount.findFirst({
        where: { name: account.name }
      });

      if (!existing) {
        await prisma.wechatAccount.create({
          data: account
        });
        console.log(`✅ 添加公众号: ${account.name}`);
      } else {
        console.log(`⚠️ 公众号已存在: ${account.name}`);
      }
    }

    console.log('🎉 测试公众号添加完成！');

    // 显示统计信息
    const totalAccounts = await prisma.wechatAccount.count();
    console.log(`📊 当前公众号总数: ${totalAccounts}`);

  } catch (error) {
    console.error('❌ 添加测试公众号失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedAccounts();
