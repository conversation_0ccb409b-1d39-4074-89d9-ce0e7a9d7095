import { WechatCrawlerScheduler } from './wechat-crawler-scheduler';

/**
 * 应用启动时的初始化函数
 */
export function initializeApp() {
  console.log('🚀 初始化应用...');
  
  // 启动微信公众号文章定时爬取任务（默认启动）
  if (process.env.DISABLE_CRAWLER_SCHEDULER !== 'true') {
    console.log('🕐 启动定时爬取任务...');
    const scheduler = WechatCrawlerScheduler.getInstance();
    scheduler.start();
  } else {
    console.log('⏸️ 定时爬取任务已被禁用');
    console.log('💡 如需启动，请移除环境变量: DISABLE_CRAWLER_SCHEDULER');
  }
  
  console.log('✅ 应用初始化完成');
}

/**
 * 应用关闭时的清理函数
 */
export function cleanupApp() {
  console.log('🛑 应用关闭，清理资源...');
  
  // 停止定时任务
  const scheduler = WechatCrawlerScheduler.getInstance();
  scheduler.stop();
  
  console.log('✅ 资源清理完成');
}
