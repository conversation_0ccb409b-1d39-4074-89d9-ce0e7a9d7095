import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';

// 批量同步订阅文章
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.SUBSCRIPTION_MANAGEMENT)(user);

    // 获取所有活跃订阅
    const activeSubscriptions = await prisma.subscription.findMany({
      where: { isActive: true },
      include: {
        wechatAccount: {
          select: {
            id: true,
            name: true,
            openid: true
          }
        }
      }
    });

    // 定义同步结果类型
    interface SyncResult {
      accountName: string;
      articleCount: number;
      status: 'success' | 'error';
      error?: string;
    }

    // 模拟同步过程（在实际应用中，这里会调用爬虫服务）
    const syncResults: SyncResult[] = [];
    
    for (const subscription of activeSubscriptions) {
      try {
        // 这里应该调用爬虫服务来获取最新文章
        // const articles = await crawlerService.getArticles(subscription.wechatAccount.openid);
        
        // 模拟同步结果
        const mockArticleCount = Math.floor(Math.random() * 5) + 1;
        
        // 更新订阅的最后同步时间
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { updatedAt: new Date() }
        });

        syncResults.push({
          accountName: subscription.wechatAccount.name,
          articleCount: mockArticleCount,
          status: 'success'
        });
      } catch (error) {
        syncResults.push({
          accountName: subscription.wechatAccount.name,
          articleCount: 0,
          status: 'error',
          error: error instanceof Error ? error.message : '同步失败'
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `同步完成，处理了 ${activeSubscriptions.length} 个订阅`,
      results: syncResults
    });
  } catch (error) {
    console.error('批量同步失败:', error);
    const errorMessage = error instanceof Error ? error.message : '批量同步失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
