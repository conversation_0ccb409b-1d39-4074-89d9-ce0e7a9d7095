import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getUserFromRequest } from '../../../lib/auth';

const prisma = new PrismaClient();

// 删除订阅
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { id: subscriptionId } = await params;

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID required' }, { status: 400 });
    }

    // 验证订阅是否属于当前用户
    const subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId }
    });

    if (!subscription || subscription.userId !== user.id) {
      return NextResponse.json({ error: '订阅不存在或无权限' }, { status: 404 });
    }

    await prisma.subscription.delete({
      where: { id: subscriptionId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('删除订阅失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
