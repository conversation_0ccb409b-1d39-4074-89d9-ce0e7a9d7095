# 微信公众号爬虫架构说明

## 文件用途分析

### 1. `lib/wechat-crawler-scheduler.ts`
**用途**: 内部定时调度器类
- 单例模式的调度器
- 用于在应用启动时创建内部定时任务
- 适用于长期运行的服务器环境
- 包含完整的爬取逻辑和错误处理

**特点**:
- 自动启动/停止机制
- 连续失败计数和告警
- 时间范围控制（6:00-23:00）
- 冷却期机制

### 2. `app/api/cron/crawl-articles/route.ts`
**用途**: Vercel Cron Job API端点
- 外部定时触发的HTTP接口
- 适用于Serverless环境（如Vercel）
- 通过HTTP请求触发爬取任务
- 包含身份验证机制

**特点**:
- HTTP GET接口
- CRON_SECRET验证
- 时间范围控制
- 适配Vercel部署环境

## 代码重复问题

两个文件确实存在代码重复，主要体现在：
1. 爬取逻辑重复
2. 时间范围检查重复
3. 公众号筛选逻辑重复
4. 错误处理逻辑重复

## 优化建议

### 方案1: 提取共同逻辑
创建一个共享的爬取服务类，两个文件都调用这个服务。

### 方案2: 统一使用API端点
让内部调度器也通过HTTP请求调用API端点，避免逻辑重复。

### 方案3: 环境适配
根据部署环境自动选择使用哪种方式。

## 当前使用场景

### 开发环境
- 使用 `WechatCrawlerScheduler` 内部调度
- 便于调试和测试

### 生产环境（Vercel）
- 使用 `/api/cron/crawl-articles` API端点
- 配合Vercel Cron Jobs
- 更稳定的Serverless执行

## 推荐架构

```
┌─────────────────────────────────────┐
│           爬取核心服务               │
│    WechatCrawlerService.ts          │
│  - 爬取逻辑                         │
│  - 错误处理                         │
│  - 通知发送                         │
└─────────────────────────────────────┘
                    ↑
        ┌───────────┴───────────┐
        │                       │
┌───────────────┐    ┌──────────────────┐
│  内部调度器    │    │   API端点        │
│  Scheduler.ts │    │   /api/cron/...  │
│  - 定时启动    │    │   - HTTP触发     │
│  - 适用长期运行 │    │   - 适用Serverless│
└───────────────┘    └──────────────────┘
```

这样可以：
1. 消除代码重复
2. 便于维护和测试
3. 支持多种部署方式
4. 统一错误处理和日志记录
