import { Metadata, Viewport } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: string;
}

export const defaultSEO: SEOConfig = {
  title: 'FeedWe - 微信公众号订阅管理工具',
  description: '专业的微信公众号订阅管理平台，支持批量订阅、文章推送、历史文章查看。让您轻松管理所有关注的公众号，不错过任何精彩内容。',
  keywords: [
    '微信公众号',
    '订阅管理',
    '文章推送',
    '公众号工具',
    '微信订阅',
    '内容聚合',
    '文章阅读',
    '公众号助手',
    'RSS订阅',
    '内容管理'
  ],
  image: '/images/og-image.jpg',
  type: 'website'
};

export function generateMetadata(config: Partial<SEOConfig> = {}): Metadata {
  const seo = { ...defaultSEO, ...config };
  
  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://feedwe.top'),
    title: seo.title,
    description: seo.description,
    keywords: seo.keywords?.join(', '),
    authors: [{ name: 'FeedWe Team' }],
    creator: 'FeedWe',
    publisher: 'FeedWe',
    
    // Open Graph
    openGraph: {
      title: seo.title,
      description: seo.description,
      url: seo.url,
      siteName: 'FeedWe',
      images: [
        {
          url: seo.image || '/images/og-image.jpg',
          width: 1200,
          height: 630,
          alt: seo.title,
        }
      ],
      locale: 'zh_CN',
      type: seo.type as any || 'website',
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: seo.title,
      description: seo.description,
      images: [seo.image || '/images/og-image.jpg'],
      creator: '@FeedWe',
    },
    
    // 其他元数据
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // 移动端优化已移至单独的viewport导出
    
    // 验证
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
    },

    // 其他元标签
    ...(process.env.BAIDU_SITE_VERIFICATION && {
      other: {
        'baidu-site-verification': process.env.BAIDU_SITE_VERIFICATION,
      }
    }),
  };
}

// 生成viewport配置
export function generateViewport(): Viewport {
  return {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  };
}

// 页面特定的SEO配置
export const pageSEO = {
  home: {
    title: 'FeedWe - 专业的微信公众号订阅管理工具',
    description: '一站式微信公众号订阅管理平台，支持批量订阅、智能推送、历史文章查看。提高阅读效率，不错过任何精彩内容。免费试用，简单易用。',
    keywords: ['微信公众号订阅', '公众号管理工具', '文章聚合', '内容订阅', '微信助手']
  },
  
  pricing: {
    title: '价格方案 - FeedWe微信公众号订阅工具',
    description: '查看FeedWe的价格方案，每个公众号仅需2元/月，支持批量订阅、文章推送等功能。提供免费试用，按需付费，性价比超高。',
    keywords: ['微信公众号价格', '订阅工具费用', '公众号管理价格', '内容订阅费用']
  },
  
  subscriptions: {
    title: '我的订阅 - FeedWe',
    description: '管理您的微信公众号订阅，查看最新文章，设置推送偏好。轻松掌控所有关注的公众号内容。',
    keywords: ['订阅管理', '公众号列表', '文章推送', '订阅设置']
  },
  
  articles: {
    title: '文章列表 - FeedWe',
    description: '浏览所有订阅公众号的最新文章，支持搜索、筛选、导出。高效阅读，不错过精彩内容。',
    keywords: ['公众号文章', '文章阅读', '内容浏览', '文章搜索']
  },
  
  login: {
    title: '登录 - FeedWe微信公众号订阅工具',
    description: '登录FeedWe账户，开始管理您的微信公众号订阅。安全便捷的登录体验。',
    keywords: ['用户登录', '账户登录', 'FeedWe登录']
  },
  
  register: {
    title: '注册 - FeedWe微信公众号订阅工具',
    description: '免费注册FeedWe账户，开始您的微信公众号订阅管理之旅。简单快速的注册流程。',
    keywords: ['用户注册', '免费注册', '账户注册', 'FeedWe注册']
  }
};
