# FeedWe - 微信公众号订阅系统

一个基于 Next.js 的微信公众号订阅和历史文章查看系统。用户可以搜索并订阅微信公众号，查看公众号的历史文章。

## 功能特性

- 🔍 **公众号搜索**: 通过名称搜索微信公众号
- ➕ **订阅管理**: 添加和取消公众号订阅
- 📰 **文章浏览**: 查看订阅公众号的历史文章
- 📱 **响应式设计**: 支持桌面端和移动端
- 🎨 **现代化UI**: 使用 Tailwind CSS 构建的美观界面

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript
- **样式**: Tailwind CSS 4
- **数据库**: MySQL + Prisma ORM
- **部署**: Vercel (Web应用) + Docker (定时任务)

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd FeedWe
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

创建 `.env` 文件并配置数据库连接：

```env
DATABASE_URL="mysql://username:password@localhost:3306/feedwe"
JWT_SECRET="your_jwt_secret_here"
```

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# (可选) 初始化演示数据
npx tsx scripts/seed.ts
```

### 5. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 生产部署

FeedWe 采用分离式部署架构：

- **Vercel 部署**: 负责 Web 界面和 API 服务
- **Docker 服务**: 负责定时任务和 Webhook 通知

### 快速部署

1. **部署 Web 应用到 Vercel**
   ```bash
   # 设置环境变量 DISABLE_CRAWLER_SCHEDULER=true
   vercel --prod
   ```

2. **部署定时任务服务到 Docker**
   ```bash
   # 配置环境变量
   cp .env.crawler.example .env.crawler

   # 一键部署
   chmod +x docker/deploy.sh
   ./docker/deploy.sh
   ```

详细部署说明请参考 [部署指南](DEPLOYMENT.md)。

## 项目结构

```
FeedWe/
├── app/                          # Next.js App Router
│   ├── api/                      # API 路由
│   │   ├── accounts/            # 公众号相关 API
│   │   ├── articles/            # 文章相关 API
│   │   ├── search-wechat-account/ # 搜索 API
│   │   └── subscriptions/       # 订阅相关 API
│   ├── articles/                # 文章页面
│   ├── subscriptions/           # 订阅管理页面
│   ├── globals.css              # 全局样式
│   ├── layout.tsx               # 根布局
│   └── page.tsx                 # 首页
├── prisma/                      # 数据库配置
│   ├── migrations/              # 数据库迁移文件
│   └── schema.prisma            # 数据库模式
├── scripts/                     # 工具脚本
│   └── seed.ts                  # 数据初始化脚本
└── public/                      # 静态资源
```

## 主要页面

### 1. 首页 (`/`)
- 搜索微信公众号
- 查看搜索结果
- 订阅公众号

### 2. 订阅管理 (`/subscriptions`)
- 查看已订阅的公众号列表
- 取消订阅
- 跳转到文章列表

### 3. 文章列表 (`/articles/[accountId]`)
- 查看特定公众号的历史文章
- 支持分页加载
- 文章预览

### 4. 文章详情 (`/articles/[accountId]/[articleId]`)
- 查看文章完整内容
- 支持富文本显示
- 文章统计信息

## API 接口

### 公众号搜索
```
POST /api/search-wechat-account
Body: { name: string }
```

### 订阅管理
```
GET /api/subscriptions?userId=xxx    # 获取用户订阅列表
POST /api/subscriptions              # 创建订阅
DELETE /api/subscriptions/[id]       # 取消订阅
```

### 文章相关
```
GET /api/articles?accountId=xxx&page=1&limit=10  # 获取文章列表
GET /api/articles/[id]                           # 获取文章详情
```

### 公众号信息
```
GET /api/accounts                    # 获取所有公众号
GET /api/accounts/[id]              # 获取单个公众号信息
```

## 数据库模型

### User (用户)
- id: 用户ID
- email: 邮箱
- name: 用户名

### WechatAccount (微信公众号)
- id: 公众号ID
- name: 公众号名称
- avatar: 头像URL
- openid: 微信OpenID
- description: 描述

### Subscription (订阅关系)
- id: 订阅ID
- userId: 用户ID
- wechatAccountId: 公众号ID
- createdAt: 创建时间

### Article (文章)
- id: 文章ID
- title: 标题
- content: 内容
- summary: 摘要
- publishDate: 发布时间
- readCount: 阅读数
- likeCount: 点赞数
- coverImage: 封面图
- wechatAccountId: 所属公众号ID

## 开发说明

### 添加新的公众号数据源

1. 修改 `app/api/search-wechat-account/route.ts` 中的搜索逻辑
2. 集成真实的第三方API接口
3. 更新数据模型以支持更多字段

### 自定义样式

项目使用 Tailwind CSS，可以在以下文件中自定义样式：
- `app/globals.css`: 全局样式和自定义CSS类
- `tailwind.config.js`: Tailwind 配置

### 部署

推荐使用 Vercel 部署：

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署完成

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 许可证

MIT License
