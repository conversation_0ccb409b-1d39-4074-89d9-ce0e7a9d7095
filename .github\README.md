# GitHub Actions 工作流

这个目录包含了 FeedWe 项目的 GitHub Actions 自动化工作流。

## 📋 工作流列表

### 🔨 docker-build.yml
**用途**: 自动构建和推送 Docker 镜像到阿里云镜像仓库

**触发条件**:
- 推送到 `main` 或 `master` 分支
- 修改 Docker 相关文件
- 手动触发

**功能**:
- 多平台构建 (linux/amd64, linux/arm64)
- 自动推送到阿里云镜像仓库
- 构建缓存优化
- 自动更新 docker-compose 文件

### 🚀 docker-release.yml
**用途**: 发布稳定版本的 Docker 镜像

**触发条件**:
- 创建 GitHub Release
- 手动触发（指定版本号）

**功能**:
- 版本化镜像构建
- 生成发布说明
- 多标签推送 (version, stable, latest)

## ⚙️ 设置步骤

### 1. 配置 GitHub Secrets

在仓库的 `Settings > Secrets and variables > Actions` 中添加：

- `ALIYUN_REGISTRY_USERNAME` - 阿里云镜像仓库用户名
- `ALIYUN_REGISTRY_PASSWORD` - 阿里云镜像仓库密码

### 2. 运行设置脚本

```bash
chmod +x scripts/setup-aliyun-registry.sh
./scripts/setup-aliyun-registry.sh
```

### 3. 推送代码触发构建

```bash
git push origin main
```

## 🐳 镜像使用

构建完成后，可以使用以下镜像：

```bash
# 最新版本
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:latest

# 特定版本
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:v1.0.0

# 稳定版本
docker pull registry.cn-hangzhou.aliyuncs.com/feedwe/crawler:stable
```

## 📚 详细文档

更多详细信息请参考：
- [GitHub Actions 设置指南](../docs/github-actions-setup.md)
- [Docker 部署指南](../docs/docker-deployment.md)
- [部署总结](../DOCKER_DEPLOYMENT_SUMMARY.md)
