import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.USER_MANAGEMENT)(user);

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';

    // 构建查询条件
    const whereCondition: any = {};

    // 搜索条件
    if (search) {
      whereCondition.OR = [
        {
          email: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          name: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // 状态筛选
    if (status !== 'all') {
      whereCondition.isActive = status === 'active';
    }

    // 获取用户总数
    const totalUsers = await prisma.user.count({
      where: whereCondition
    });

    // 获取分页用户数据
    const users = await prisma.user.findMany({
      where: whereCondition,
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        updatedAt: true,
        isActive: true,
        inviteCode: true,
        invitedBy: true,
        inviter: {
          select: {
            email: true
          }
        },
        _count: {
          select: {
            subscriptions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    // 转换数据格式
    const formattedUsers = users.map(user => ({
      id: user.id,
      email: user.email,
      name: user.name,
      createdAt: user.createdAt.toISOString(),
      lastLoginAt: user.updatedAt.toISOString(), // 暂时使用updatedAt作为lastLoginAt
      isActive: user.isActive,
      subscriptionCount: user._count.subscriptions,
      inviteCode: user.inviteCode,
      invitedBy: user.inviter?.email || null
    }));

    const totalPages = Math.ceil(totalUsers / limit);

    return NextResponse.json({
      users: formattedUsers,
      totalPages,
      currentPage: page,
      totalUsers
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取用户列表失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
