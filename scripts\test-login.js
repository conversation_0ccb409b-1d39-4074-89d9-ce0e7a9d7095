const { default: fetch } = require('node-fetch');

async function testLogin() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123456'
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 登录成功!');
      console.log('👤 用户信息:', data.user);
      console.log('🔑 Token:', data.token ? '已生成' : '未生成');
      
      if (data.user.role) {
        console.log('🎯 用户角色:', data.user.role);
        console.log('🔓 管理员权限:', data.user.role === 'SUPER_ADMIN' ? '是' : '否');
      } else {
        console.log('❌ 用户角色信息缺失');
      }
    } else {
      console.log('❌ 登录失败:', data.error);
    }
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testLogin();
