import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function initProxyServers() {
  console.log('🚀 开始初始化代理服务器...');

  try {
    // 检查是否已有代理服务器
    const existingCount = await prisma.proxyServer.count();
    if (existingCount > 0) {
      console.log(`⚠️ 已存在 ${existingCount} 个代理服务器，跳过初始化`);
      return;
    }

    // 添加示例代理服务器
    const proxyServers = [
      {
        name: '代理服务器 1',
        url: 'https://proxy1.example.com/api/proxy',
        isActive: true,
        priority: 1,
        maxRetries: 3,
        timeout: 30
      },
      {
        name: '代理服务器 2',
        url: 'https://proxy2.example.com/api/proxy',
        isActive: true,
        priority: 2,
        maxRetries: 3,
        timeout: 30
      },
      {
        name: '备用代理服务器',
        url: 'https://backup-proxy.example.com/api/proxy',
        isActive: false,
        priority: 10,
        maxRetries: 2,
        timeout: 20
      }
    ];

    // 批量创建代理服务器
    const created = await prisma.proxyServer.createMany({
      data: proxyServers
    });

    console.log(`✅ 成功创建 ${created.count} 个代理服务器`);

    // 显示创建的代理服务器
    const allProxies = await prisma.proxyServer.findMany({
      orderBy: { priority: 'asc' }
    });

    console.log('\n📋 代理服务器列表:');
    allProxies.forEach((proxy, index) => {
      console.log(`${index + 1}. ${proxy.name}`);
      console.log(`   URL: ${proxy.url}`);
      console.log(`   状态: ${proxy.isActive ? '启用' : '禁用'}`);
      console.log(`   优先级: ${proxy.priority}`);
      console.log(`   配置: 重试${proxy.maxRetries}次, 超时${proxy.timeout}秒`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ 初始化代理服务器失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initProxyServers()
    .then(() => {
      console.log('🎉 代理服务器初始化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 初始化失败:', error);
      process.exit(1);
    });
}

export { initProxyServers };
