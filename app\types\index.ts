export type WechatAccount = {
  id: string;
  name: string;
  avatar: string;
  description?: string;
};

export type Article = {
  id: string;
  title: string;
  summary?: string;
  url: string;
  publishDate: string;
  readCount?: number;
  likeCount?: number;
  coverImage?: string;
  author?: string;
  tags?: string;
  wechatAccount: WechatAccount;
};

export type Subscription = {
  id: string;
  userId: string;
  wechatAccountId: string;
  wechatAccount: WechatAccount;
  createdAt: string;
};

export type User = {
  id: string;
  email: string;
  name: string | null;
  avatar: string | null;
  isActive: boolean;
  inviteCode: string | null;
  createdAt: string;
  updatedAt: string;
  webhookUrl?: string | null;
  webhookType?: WebhookType | null;
  webhookEnabled: boolean;
};

// 使用Prisma生成的枚举类型
export { WebhookType, DownloadStatus } from '@prisma/client';
import type { WebhookType, DownloadStatus } from '@prisma/client';

export type ProxyServer = {
  id: string;
  name: string;
  url: string;
  isActive: boolean;
  priority: number;
  maxRetries: number;
  timeout: number;
  createdAt: string;
  updatedAt: string;
};

export type ArticleDownload = {
  id: string;
  articleId: string;
  userId: string;
  status: DownloadStatus;
  downloadUrl?: string;
  filePath?: string;
  fileSize?: number;
  errorMessage?: string;
  proxyServerId?: string;
  downloadTime?: string;
  createdAt: string;
  updatedAt: string;
  article?: Article;
  proxyServer?: ProxyServer;
};
