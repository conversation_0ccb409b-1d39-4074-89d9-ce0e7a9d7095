/**
 * 检查system_configs表的schema，验证value字段长度是否为500
 */

const { prisma } = require('../lib/prisma');

async function checkSystemConfigSchema() {
  try {
    console.log('🔍 检查system_configs表结构...\n');

    // 执行原生SQL查询来检查表结构
    const result = await prisma.$queryRaw`
      DESCRIBE system_configs;
    `;

    console.log('📋 system_configs表结构:');
    console.table(result);

    // 查找value字段的信息
    const valueField = result.find(field => field.Field === 'value');
    
    if (valueField) {
      console.log('\n📝 value字段详情:');
      console.log(`- 字段名: ${valueField.Field}`);
      console.log(`- 数据类型: ${valueField.Type}`);
      console.log(`- 是否允许NULL: ${valueField.Null}`);
      console.log(`- 默认值: ${valueField.Default || 'NULL'}`);
      
      // 检查是否为VARCHAR(500)
      if (valueField.Type === 'varchar(500)') {
        console.log('\n✅ 成功！value字段长度已更新为500');
      } else {
        console.log('\n❌ 警告！value字段类型不是varchar(500)');
        console.log(`   当前类型: ${valueField.Type}`);
      }
    } else {
      console.log('\n❌ 错误：未找到value字段');
    }

    // 测试插入一个长度接近500的值
    console.log('\n🧪 测试插入长值...');
    const testValue = 'A'.repeat(450); // 450个字符的测试值
    const testName = `test_long_value_${Date.now()}`;

    try {
      await prisma.systemConfig.create({
        data: {
          name: testName,
          value: testValue
        }
      });
      console.log(`✅ 成功插入${testValue.length}字符的值`);

      // 清理测试数据
      await prisma.systemConfig.delete({
        where: { name: testName }
      });
      console.log('🧹 测试数据已清理');

    } catch (error) {
      console.log(`❌ 插入测试失败: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkSystemConfigSchema().catch(console.error);
}

module.exports = { checkSystemConfigSchema };
