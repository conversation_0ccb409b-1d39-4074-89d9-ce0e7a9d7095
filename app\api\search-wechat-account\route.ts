import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function POST(req: NextRequest) {
  const { name } = await req.json();
  if (!name) {
    return NextResponse.json({ error: 'name required' }, { status: 400 });
  }

  try {
    console.log('🔍 搜索数据库中的公众号:', name);

    // 搜索数据库中的公众号
    const accounts = await prisma.wechatAccount.findMany({
      where: {
        OR: [
          {
            name: {
              contains: name
            }
          },
          {
            nickname: {
              contains: name
            }
          },
          {
            description: {
              contains: name
            }
          },
          {
            signature: {
              contains: name
            }
          }
        ]
      },
      select: {
        id: true,
        name: true,
        nickname: true,
        avatar: true,
        openid: true,
        description: true,
        signature: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // 限制返回数量
    });

    // 处理微信图片URL，使用代理
    const results = accounts.map(account => ({
      id: account.id,
      name: account.name || account.nickname || '',
      avatar: account.avatar && (
        account.avatar.includes('mmbiz.qpic.cn') ||
        account.avatar.includes('mmbiz.qlogo.cn') ||
        account.avatar.includes('wx.qlogo.cn') ||
        account.avatar.includes('thirdwx.qlogo.cn')
      ) ? `/api/proxy/wechat-image?url=${encodeURIComponent(account.avatar)}`
        : account.avatar,
      openid: account.openid || '',
      description: account.description || account.signature || ''
    }));

    console.log(`✅ 找到 ${results.length} 个匹配的公众号`);

    return NextResponse.json(results);
  } catch (error) {
    console.error('搜索公众号失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}