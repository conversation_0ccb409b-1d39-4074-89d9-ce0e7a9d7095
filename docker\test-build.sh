#!/bin/bash

# 本地测试构建脚本

set -e

echo "🧪 测试 Docker 构建..."

# 构建测试镜像
echo "🔨 构建测试镜像..."
docker build -f Dockerfile.crawler -t feedwe-crawler-test:latest .

echo "✅ 构建成功！"

# 可选：运行测试容器
read -p "是否运行测试容器？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动测试容器..."
    
    # 检查环境变量文件
    if [ ! -f ".env.crawler" ]; then
        echo "⚠️ 警告: .env.crawler 文件不存在，使用默认配置"
        export DATABASE_URL="mysql://test:test@localhost:3306/test"
        export DISABLE_CRAWLER_SCHEDULER="false"
    else
        export $(cat .env.crawler | grep -v '^#' | xargs)
    fi
    
    # 运行测试容器
    docker run --rm -it \
        -p 3001:3001 \
        -e DATABASE_URL="$DATABASE_URL" \
        -e DISABLE_CRAWLER_SCHEDULER="$DISABLE_CRAWLER_SCHEDULER" \
        -e NODE_ENV=production \
        feedwe-crawler-test:latest
else
    echo "📋 测试完成。镜像标签: feedwe-crawler-test:latest"
    echo "🗑️ 清理测试镜像: docker rmi feedwe-crawler-test:latest"
fi
