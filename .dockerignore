# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# 开发文件
.env*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# 版本控制
.git/
.gitignore

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 日志
logs/
*.log

# 测试
coverage/
.nyc_output/

# 文档
docs/
README.md
*.md
!docker/README.md

# 脚本（除了必要的）
scripts/
!scripts/build-and-push.sh
!scripts/deploy-from-registry.sh

# 应用相关（不需要的）
app/
components/
public/
styles/

# 构建相关
.vercel/
.github/
!.github/workflows/

# 备份文件
*.backup
*.bak
*.tmp

# Docker相关
Dockerfile*
!Dockerfile.crawler
docker-compose*.yml
!docker-compose.crawler.yml
