version: '3.8'

services:
  feedwe-crawler:
    # 开发环境使用本地构建
    build:
      context: .
      dockerfile: Dockerfile.crawler
    container_name: feedwe-crawler-dev
    restart: unless-stopped
    environment:
      # 从环境变量文件加载
      - DATABASE_URL
      - WECHAT_ARTICLE_COOKIE_STRING
      - WECHAT_ARTICLE_TOKEN
      - WEWORK_WEBHOOK_URL
      - DISABLE_CRAWLER_SCHEDULER=false
      - NODE_ENV=development
      - TZ=Asia/Shanghai
    
    ports:
      - "3001:3001"
    
    volumes:
      - ./logs:/app/logs
      - .:/app  # 开发环境挂载源码
      - /app/node_modules  # 排除 node_modules
    
    env_file:
      - .env.crawler
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    networks:
      - feedwe-network

networks:
  feedwe-network:
    driver: bridge

volumes:
  logs:
    driver: local
