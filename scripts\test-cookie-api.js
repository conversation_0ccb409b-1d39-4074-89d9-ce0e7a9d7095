const fetch = require('node-fetch');

async function testCookieAPI() {
  try {
    console.log('🍪 测试Cookie API...');

    // 模拟管理员token（需要先登录获取真实token）
    const adminToken = 'your-admin-token-here'; // 这里需要替换为真实的管理员token
    
    const baseUrl = 'http://localhost:3000';
    
    // 测试1: 获取Cookie信息
    console.log('\n=== 测试1: 获取Cookie信息 ===');
    try {
      const response = await fetch(`${baseUrl}/api/crawler/cookies`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      const data = await response.json();
      console.log('📊 响应状态:', response.status);
      console.log('📊 响应数据:', data);
      
      if (response.ok) {
        console.log('✅ 获取Cookie信息成功');
      } else {
        console.log('❌ 获取Cookie信息失败:', data.error);
      }
    } catch (error) {
      console.error('❌ 请求失败:', error.message);
    }

    // 测试2: 保存Cookie信息
    console.log('\n=== 测试2: 保存Cookie信息 ===');
    const mockCookieData = {
      sessionId: 'test_session_' + Date.now(),
      cookieParams: {
        data_ticket: 'mock_data_ticket_123',
        rand_info: 'mock_rand_info_456',
        bizuin: 'mock_bizuin_789',
        slave_sid: 'mock_slave_sid_abc',
        slave_user: 'mock_slave_user_def'
      },
      token: 'mock_wechat_token_' + Date.now()
    };

    try {
      const response = await fetch(`${baseUrl}/api/crawler/cookies`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(mockCookieData)
      });
      
      const data = await response.json();
      console.log('📊 响应状态:', response.status);
      console.log('📊 响应数据:', data);
      
      if (response.ok) {
        console.log('✅ 保存Cookie信息成功');
      } else {
        console.log('❌ 保存Cookie信息失败:', data.error);
      }
    } catch (error) {
      console.error('❌ 请求失败:', error.message);
    }

    // 测试3: 再次获取Cookie信息验证保存
    console.log('\n=== 测试3: 验证Cookie保存 ===');
    try {
      const response = await fetch(`${baseUrl}/api/crawler/cookies`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      const data = await response.json();
      console.log('📊 响应状态:', response.status);
      console.log('📊 响应数据:', data);
      
      if (response.ok && data.hasValidCookie) {
        console.log('✅ Cookie验证成功，已保存有效凭证');
      } else {
        console.log('❌ Cookie验证失败或无有效凭证');
      }
    } catch (error) {
      console.error('❌ 请求失败:', error.message);
    }

    // 测试4: 清除Cookie
    console.log('\n=== 测试4: 清除Cookie ===');
    try {
      const response = await fetch(`${baseUrl}/api/crawler/cookies`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      const data = await response.json();
      console.log('📊 响应状态:', response.status);
      console.log('📊 响应数据:', data);
      
      if (response.ok) {
        console.log('✅ 清除Cookie成功');
      } else {
        console.log('❌ 清除Cookie失败:', data.error);
      }
    } catch (error) {
      console.error('❌ 请求失败:', error.message);
    }

    console.log('\n🎉 Cookie API测试完成！');
    console.log('\n💡 注意事项:');
    console.log('1. 需要先登录管理后台获取有效的管理员token');
    console.log('2. 将上面的 adminToken 替换为真实token');
    console.log('3. 确保服务器正在运行在 http://localhost:3000');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testCookieAPI();
