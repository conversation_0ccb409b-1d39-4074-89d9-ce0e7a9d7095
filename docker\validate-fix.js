#!/usr/bin/env node

/**
 * 验证修复的脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证 Docker 构建修复...');

// 检查关键文件
const checks = [
  {
    file: 'docker/crawler-service.js',
    check: (content) => content.includes('./lib/wechat-crawler-scheduler.ts'),
    message: 'crawler-service.js 导入 TypeScript 文件'
  },
  {
    file: 'docker/package.json',
    check: (content) => content.includes('"tsx"'),
    message: 'package.json 包含 tsx 依赖'
  },
  {
    file: 'Dockerfile.crawler',
    check: (content) => content.includes('npx", "tsx'),
    message: 'Dockerfile 使用 tsx 启动'
  },
  {
    file: 'lib/wechat-crawler-scheduler.ts',
    check: (content) => content.length > 0,
    message: 'TypeScript 源文件存在'
  },
  {
    file: 'lib/startup.ts',
    check: (content) => content.length > 0,
    message: 'startup.ts 文件存在'
  }
];

let allPassed = true;

for (const { file, check, message } of checks) {
  const filePath = path.join(__dirname, '..', file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${message} - 文件不存在: ${file}`);
    allPassed = false;
    continue;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  if (check(content)) {
    console.log(`✅ ${message}`);
  } else {
    console.log(`❌ ${message} - 检查失败`);
    allPassed = false;
  }
}

if (allPassed) {
  console.log('\n🎉 所有检查通过！');
  console.log('\n📋 修复摘要:');
  console.log('1. ✅ 修改 crawler-service.js 导入 TypeScript 文件');
  console.log('2. ✅ 添加 tsx 依赖到 package.json');
  console.log('3. ✅ 修改 Dockerfile 使用 tsx 运行');
  console.log('4. ✅ 确保所有必需的 TypeScript 文件存在');
  
  console.log('\n🚀 下一步:');
  console.log('1. 重新构建 Docker 镜像');
  console.log('2. 测试容器启动');
  console.log('3. 验证健康检查端点');
  
  console.log('\n💡 构建命令:');
  console.log('docker-compose -f docker-compose.crawler.yml up --build -d');
} else {
  console.log('\n❌ 存在问题，请检查上述失败项');
  process.exit(1);
}

console.log('\n📝 注意事项:');
console.log('- 确保 Docker 正在运行');
console.log('- 检查环境变量配置');
console.log('- 验证数据库连接');
