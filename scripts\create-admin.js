const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    // 管理员信息
    const adminData = {
      email: '<EMAIL>',
      name: '超级管理员',
      password: 'admin123456', // 请在生产环境中使用强密码
      role: 'SUPER_ADMIN'
    };

    // 检查管理员是否已存在
    const existingAdmin = await prisma.user.findUnique({
      where: { email: adminData.email }
    });

    if (existingAdmin) {
      console.log('管理员账户已存在:', adminData.email);
      return;
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(adminData.password, 12);

    // 创建管理员用户
    const admin = await prisma.user.create({
      data: {
        email: adminData.email,
        name: adminData.name,
        password: hashedPassword,
        role: adminData.role,
        isActive: true,
        inviteCode: 'ADMIN001'
      }
    });

    console.log('✅ 管理员账户创建成功!');
    console.log('📧 邮箱:', adminData.email);
    console.log('🔑 密码:', adminData.password);
    console.log('👤 角色:', adminData.role);
    console.log('');
    console.log('🚀 现在可以使用以下步骤登录管理后台:');
    console.log('1. 访问 /auth/login');
    console.log('2. 使用上述邮箱和密码登录');
    console.log('3. 登录后点击用户头像 -> 管理后台');
    console.log('4. 或直接访问 /admin');

  } catch (error) {
    console.error('❌ 创建管理员失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
