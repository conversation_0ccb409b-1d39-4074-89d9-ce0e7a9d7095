'use client';

import React from 'react';
import Navigation from '../components/Navigation';
import Link from 'next/link';

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <Navigation currentPage="docs" />

      <div className="max-w-4xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            使用文档
          </h1>
          <p className="mt-4 text-xl text-gray-600">
            快速上手微信公众号订阅工具
          </p>
        </div>

        {/* 快速开始 */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <svg className="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            快速开始
          </h2>
          
          <div className="space-y-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">1</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">注册账户</h3>
                <p className="text-gray-600 mt-1">
                  访问 <Link href="/auth/register" className="text-blue-600 hover:underline">注册页面</Link>，
                  创建您的账户。如果有邀请码，可以享受专属优惠。
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">2</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">添加订阅</h3>
                <p className="text-gray-600 mt-1">
                  登录后，在 <Link href="/subscriptions" className="text-blue-600 hover:underline">订阅管理</Link> 页面
                  点击"添加订阅"按钮，搜索并订阅您感兴趣的微信公众号。
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">3</span>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">查看文章</h3>
                <p className="text-gray-600 mt-1">
                  在 <Link href="/feed" className="text-blue-600 hover:underline">文章阅读</Link> 页面
                  浏览订阅公众号的历史文章，支持搜索和时间筛选。
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 功能介绍 */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <svg className="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            主要功能
          </h2>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">📚 历史文章查看</h3>
              <p className="text-gray-600 mb-4">
                查看订阅公众号的所有历史文章，无需翻阅微信聊天记录。
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 按时间排序显示</li>
                <li>• 支持关键词搜索</li>
                <li>• 支持时间范围筛选</li>
              </ul>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">📊 数据导出</h3>
              <p className="text-gray-600 mb-4">
                将文章数据导出为Excel文件，方便进行数据分析和备份。
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 包含标题、链接、时间</li>
                <li>• 包含作者和标签信息</li>
                <li>• 支持筛选条件导出</li>
              </ul>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">🔔 推送通知</h3>
              <p className="text-gray-600 mb-4">
                当订阅的公众号发布新文章时，自动推送通知到您的工作平台。
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 支持企业微信推送</li>
                <li>• 支持钉钉推送</li>
                <li>• 支持飞书推送</li>
              </ul>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">💰 邀请返现</h3>
              <p className="text-gray-600 mb-4">
                邀请好友注册使用，获得丰厚的返现奖励。
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 邀请人获得50%返现</li>
                <li>• 被邀请人享受优惠价格</li>
                <li>• 实时查看邀请统计</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <svg className="w-6 h-6 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            常见问题
          </h2>

          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">如何添加公众号订阅？</h3>
              <p className="text-gray-600">
                在订阅管理页面点击"添加订阅"按钮，输入公众号名称进行搜索，选择要订阅的公众号即可。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">文章内容在哪里查看？</h3>
              <p className="text-gray-600">
                点击文章标题会跳转到文章详情页，然后点击"阅读原文"按钮会在新窗口中打开微信公众号的原文链接。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">如何设置推送通知？</h3>
              <p className="text-gray-600">
                目前推送功能正在开发中，敬请期待。您可以通过邮件或微信联系我们了解最新进展。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">数据导出包含哪些信息？</h3>
              <p className="text-gray-600">
                导出的Excel文件包含文章标题、原文链接、发布时间、作者、摘要、阅读数、点赞数、标签等完整信息。
              </p>
            </div>
          </div>
        </div>

        {/* 联系支持 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">需要帮助？</h2>
          <p className="text-gray-600 mb-6">
            如果您在使用过程中遇到任何问题，欢迎随时联系我们
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/pricing"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              联系我们
            </Link>
            <Link 
              href="/subscriptions"
              className="inline-flex items-center px-6 py-3 bg-white hover:bg-gray-50 text-gray-700 font-medium rounded-lg border border-gray-300 transition-colors"
            >
              开始使用
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
