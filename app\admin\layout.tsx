'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '../components/AuthProvider';
import { isAdmin, hasPermission, Permission } from '../../lib/auth/admin';

const menuItems = [
  {
    name: '仪表板',
    href: '/admin',
    icon: '📊',
    permission: null
  },
  {
    name: '用户管理',
    href: '/admin/users',
    icon: '👥',
    permission: Permission.USER_MANAGEMENT
  },
  {
    name: '订阅管理',
    href: '/admin/subscriptions',
    icon: '📱',
    permission: Permission.SUBSCRIPTION_MANAGEMENT
  },
  {
    name: '公众号管理',
    href: '/admin/accounts',
    icon: '📢',
    permission: Permission.CONTENT_MODERATION
  },
  {
    name: '爬虫管理',
    href: '/admin/crawler',
    icon: '🤖',
    permission: Permission.CONTENT_MODERATION
  },
  {
    name: '数据统计',
    href: '/admin/analytics',
    icon: '📈',
    permission: Permission.ANALYTICS
  },
  {
    name: '系统设置',
    href: '/admin/settings',
    icon: '⚙️',
    permission: Permission.SYSTEM_SETTINGS
  }
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, logout } = useAuth();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // 检查管理员权限
  if (!user || !isAdmin(user)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">访问被拒绝</h1>
          <p className="text-gray-600 mb-6">您没有访问管理后台的权限</p>
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            返回首页
          </Link>
        </div>
      </div>
    );
  }

  const filteredMenuItems = menuItems.filter(item => 
    !item.permission || hasPermission(user.role, item.permission)
  );

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 侧边栏 */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-col`}>
        <div className="flex items-center justify-center h-16 bg-blue-600">
          <h1 className="text-white text-xl font-bold">管理后台</h1>
        </div>
        
        <nav className="mt-8">
          {filteredMenuItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center px-6 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors ${
                pathname === item.href ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' : ''
              }`}
            >
              <span className="mr-3">{item.icon}</span>
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* 顶部导航栏 */}
        <header className="bg-white shadow-sm border-b">
          <div className="flex items-center justify-between px-6 py-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                欢迎，{user.name} ({user.role})
              </span>
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800 transition-colors"
              >
                退出登录
              </button>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>

      {/* 移动端遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
