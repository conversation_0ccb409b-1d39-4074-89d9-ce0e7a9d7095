'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Navigation from '../components/Navigation';
import { useAuth, withAuth } from '../components/AuthProvider';
import { Article, WechatAccount, Subscription } from '../types/index';
import { DownloadButton } from '../../components/DownloadButton';
import { BatchDownloadButton } from '../../components/BatchDownloadButton';

// 工具函数：检查是否为微信图片URL
const isWechatImageUrl = (url: string): boolean => {
  if (!url) return false;
  const wechatDomains = ['mmbiz.qpic.cn', 'mmbiz.qlogo.cn', 'wx.qlogo.cn', 'thirdwx.qlogo.cn'];
  return wechatDomains.some(domain => url.includes(domain));
};

// 工具函数：获取代理后的图片URL（仅用于显示）
const getDisplayImageUrl = (url: string): string => {
  if (!url) return url;
  // 如果已经是代理URL，直接返回
  if (url.startsWith('/api/proxy/wechat-image')) return url;
  // 如果不是微信图片URL，直接返回
  if (!isWechatImageUrl(url)) return url;
  return `/api/proxy/wechat-image?url=${encodeURIComponent(url)}`;
};

function FeedPage() {
  const searchParams = useSearchParams();
  const { user, token } = useAuth();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null);
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [articlesLoading, setArticlesLoading] = useState(false);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // 获取用户订阅列表
  useEffect(() => {
    fetchSubscriptions();
  }, []);

  // 当选择的公众号改变时，重新获取文章
  useEffect(() => {
    if (selectedAccountId) {
      setNextCursor(null);
      setArticles([]);
      fetchArticles(selectedAccountId, null, false);
    }
  }, [selectedAccountId, searchQuery, startDate, endDate]);

  const fetchSubscriptions = async () => {
    if (!token) return;

    try {
      const res = await fetch('/api/subscriptions', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await res.json();
      setSubscriptions(data);

      // 检查URL参数中是否指定了公众号ID
      const accountIdFromUrl = searchParams.get('accountId');
      if (accountIdFromUrl && data.some((sub: Subscription) => sub.wechatAccount.id === accountIdFromUrl)) {
        setSelectedAccountId(accountIdFromUrl);
      } else if (data.length > 0) {
        // 默认选择第一个订阅的公众号
        setSelectedAccountId(data[0].wechatAccount.id);
      }
    } catch (error) {
      console.error('获取订阅列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchArticles = async (accountId: string, cursor: string | null, append = false) => {
    if (!token) return;

    try {
      setArticlesLoading(true);
      const params = new URLSearchParams({
        accountId,
        limit: '20'
      });

      if (cursor) {
        params.append('cursor', cursor);
      }

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      if (startDate) {
        params.append('startDate', startDate);
      }

      if (endDate) {
        params.append('endDate', endDate);
      }

      const res = await fetch(`/api/articles?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await res.json();

      if (append) {
        setArticles(prev => [...prev, ...data.articles]);
      } else {
        setArticles(data.articles);
      }

      setHasMore(data.pagination.hasMore);
      setNextCursor(data.pagination.nextCursor);
    } catch (error) {
      console.error('获取文章列表失败:', error);
    } finally {
      setArticlesLoading(false);
    }
  };

  const loadMore = () => {
    if (selectedAccountId && !articlesLoading && hasMore && nextCursor) {
      fetchArticles(selectedAccountId, nextCursor, true);
    }
  };

  // 选择相关函数
  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedArticles([]);
  };

  const toggleArticleSelection = (articleId: string) => {
    setSelectedArticles(prev =>
      prev.includes(articleId)
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId]
    );
  };

  const selectAllArticles = () => {
    setSelectedArticles(articles.map(article => article.id));
  };

  const clearSelection = () => {
    setSelectedArticles([]);
  };

  // 导出Excel
  const handleExportExcel = async () => {
    if (!selectedAccountId || !token) return;

    setExporting(true);
    try {
      const params = new URLSearchParams({
        accountId: selectedAccountId
      });

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      if (startDate) {
        params.append('startDate', startDate);
      }

      if (endDate) {
        params.append('endDate', endDate);
      }

      const response = await fetch(`/api/articles/export?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `文章导出_${selectedAccount?.name || '公众号'}_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const error = await response.json();
        alert(error.error || '导出失败');
      }
    } catch (error) {
      console.error('导出Excel失败:', error);
      alert('导出失败，请稍后重试');
    } finally {
      setExporting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      weekday: 'short'
    });
  };

  const selectedAccount = subscriptions.find(sub => sub.wechatAccount.id === selectedAccountId)?.wechatAccount;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg text-gray-600">加载中...</div>
        </div>
      </div>
    );
  }

  if (subscriptions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* 顶部导航栏 */}
        <Navigation currentPage="feed" />

        <div className="flex items-center justify-center" style={{ height: 'calc(100vh - 80px)' }}>
          <div className="text-center">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
            <div className="text-gray-500 text-lg mb-4">您还没有订阅任何公众号</div>
            <p className="text-gray-400 mb-6">订阅公众号后即可查看历史文章</p>
            <Link
              href="/subscriptions"
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors inline-flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              立即添加订阅
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <Navigation currentPage="feed" />

      <div className="max-w-7xl mx-auto flex">
        {/* 左侧公众号列表 */}
        <div className="w-80 bg-white shadow-sm border-r min-h-screen sticky top-16 hidden lg:block">
          <div className="p-4 border-b">
            <h2 className="font-semibold text-gray-900">我的订阅</h2>
            <p className="text-sm text-gray-500 mt-1">{subscriptions.length} 个公众号</p>
          </div>
          
          <div className="overflow-y-auto" style={{ height: 'calc(100vh - 120px)' }}>
            {subscriptions.map((subscription) => (
              <button
                key={subscription.wechatAccount.id}
                onClick={() => setSelectedAccountId(subscription.wechatAccount.id)}
                className={`w-full p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 ${
                  selectedAccountId === subscription.wechatAccount.id 
                    ? 'bg-blue-50 border-l-4 border-l-blue-500' 
                    : ''
                }`}
              >
                <div className="flex items-center">
                  <img
                    src={getDisplayImageUrl(subscription.wechatAccount.avatar)}
                    alt={subscription.wechatAccount.name}
                    className="w-12 h-12 rounded-full mr-3 flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium truncate ${
                      selectedAccountId === subscription.wechatAccount.id 
                        ? 'text-blue-900' 
                        : 'text-gray-900'
                    }`}>
                      {subscription.wechatAccount.name}
                    </h3>
                    {subscription.wechatAccount.description && (
                      <p className="text-sm text-gray-500 truncate mt-1">
                        {subscription.wechatAccount.description}
                      </p>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 右侧文章内容 */}
        <div className="flex-1 p-4 lg:p-6">
          {selectedAccount && (
            <>
              {/* 移动端公众号选择器 */}
              <div className="lg:hidden mb-4">
                <select
                  value={selectedAccountId || ''}
                  onChange={(e) => setSelectedAccountId(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg bg-white"
                >
                  {subscriptions.map((subscription) => (
                    <option key={subscription.wechatAccount.id} value={subscription.wechatAccount.id}>
                      {subscription.wechatAccount.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* 当前选中公众号信息 */}
              <div className="bg-white rounded-lg shadow-sm p-4 lg:p-6 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <img
                      src={getDisplayImageUrl(selectedAccount.avatar)}
                      alt={selectedAccount.name}
                      className="w-16 h-16 rounded-full mr-4"
                    />
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900 mb-1">
                        {selectedAccount.name}
                      </h1>
                      {selectedAccount.description && (
                        <p className="text-gray-600">{selectedAccount.description}</p>
                      )}
                    </div>
                  </div>

                  {/* 导出按钮 */}
                  <button
                    onClick={handleExportExcel}
                    disabled={exporting || articles.length === 0}
                    className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-lg transition-colors"
                  >
                    {exporting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        导出中...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出Excel
                      </>
                    )}
                  </button>
                </div>

                {/* 搜索框 */}
                <div className="mt-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="搜索文章标题、摘要或作者..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <button
                      onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                      className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                      title="高级搜索"
                    >
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                      </svg>
                    </button>
                  </div>

                  {/* 高级搜索选项 */}
                  {showAdvancedSearch && (
                    <div className="mt-3 p-4 bg-gray-50 rounded-lg border">
                      <h4 className="text-sm font-medium text-gray-700 mb-3">时间范围</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">开始日期</label>
                          <input
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-600 mb-1">结束日期</label>
                          <input
                            type="date"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                      <div className="mt-3 flex justify-end space-x-2">
                        <button
                          onClick={() => {
                            setStartDate('');
                            setEndDate('');
                          }}
                          className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800"
                        >
                          清除
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 批量操作控制栏 */}
              {selectedAccountId && (
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={toggleSelectionMode}
                      className={`px-4 py-2 rounded-lg transition-colors text-sm ${
                        isSelectionMode
                          ? 'bg-red-500 hover:bg-red-600 text-white'
                          : 'bg-gray-500 hover:bg-gray-600 text-white'
                      }`}
                    >
                      {isSelectionMode ? '取消选择' : '批量操作'}
                    </button>

                    {isSelectionMode && (
                      <>
                        <span className="text-sm text-gray-600">
                          已选择 {selectedArticles.length} 篇文章
                        </span>
                        <button
                          onClick={selectAllArticles}
                          className="text-blue-500 hover:text-blue-600 text-sm"
                        >
                          全选
                        </button>
                        <button
                          onClick={clearSelection}
                          className="text-gray-500 hover:text-gray-600 text-sm"
                        >
                          清空
                        </button>
                      </>
                    )}
                  </div>

                  {isSelectionMode && selectedArticles.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <BatchDownloadButton
                        articleIds={selectedArticles}
                        size="sm"
                        onDownloadComplete={() => {
                          setIsSelectionMode(false);
                          setSelectedArticles([]);
                        }}
                      />
                      <button
                        onClick={handleExportExcel}
                        disabled={exporting}
                        className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white text-sm rounded-lg transition-colors disabled:opacity-50"
                      >
                        {exporting ? '导出中...' : '导出Excel'}
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* 文章列表 */}
              {articles.length === 0 && !articlesLoading ? (
                <div className="text-center py-12">
                  <div className="text-gray-500 text-lg">暂无文章</div>
                </div>
              ) : (
                <div className="space-y-4">
                  {articles.map((article) => (
                    <div
                      key={article.id}
                      className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="p-4 lg:p-6">
                        <div className="flex flex-col sm:flex-row">
                          {/* 选择框 */}
                          {isSelectionMode && (
                            <div className="mb-4 sm:mb-0 sm:mr-4 flex items-start pt-2">
                              <input
                                type="checkbox"
                                checked={selectedArticles.includes(article.id)}
                                onChange={() => toggleArticleSelection(article.id)}
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                              />
                            </div>
                          )}

                          {/* 文章封面 */}
                          {article.coverImage && (
                            <img
                              src={getDisplayImageUrl(article.coverImage)}
                              alt={article.title}
                              className="w-full sm:w-32 h-48 sm:h-24 object-cover rounded-lg mb-4 sm:mb-0 sm:mr-4 flex-shrink-0"
                            />
                          )}

                          {/* 文章内容 */}
                          <div className="flex-1">
                            <Link href={`/articles/${selectedAccountId}/${article.id}`}>
                              <h2 className="text-lg lg:text-xl font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors line-clamp-2">
                                {article.title}
                              </h2>
                            </Link>
                            {article.summary && (
                              <p className="text-gray-600 mb-3 line-clamp-2 text-sm lg:text-base">
                                {article.summary}
                              </p>
                            )}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center text-xs lg:text-sm text-gray-500">
                                <span>发布时间：{formatDate(article.publishDate)}</span>
                              </div>

                              {/* 操作按钮 */}
                              {!isSelectionMode && (
                                <div className="flex items-center space-x-2">
                                  <DownloadButton
                                    articleId={article.id}
                                    articleTitle={article.title}
                                    variant="outline"
                                    size="sm"
                                  />
                                  <a
                                    href={article.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-500 hover:text-blue-600 text-sm"
                                  >
                                    查看原文
                                  </a>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* 加载更多按钮 */}
                  {hasMore && (
                    <div className="text-center py-6">
                      <button
                        onClick={loadMore}
                        disabled={articlesLoading}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors disabled:opacity-50"
                      >
                        {articlesLoading ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            加载中...
                          </span>
                        ) : '加载更多'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default withAuth(FeedPage);
