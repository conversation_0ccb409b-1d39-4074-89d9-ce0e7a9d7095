#!/usr/bin/env node

/**
 * FeedWe 定时任务爬虫服务 - CommonJS 版本
 * 专门用于Docker部署的独立定时任务服务
 */

const http = require('http');
const { PrismaClient } = require('@prisma/client');

console.log('🚀 启动 FeedWe 定时任务爬虫服务...');
console.log(`📅 启动时间: ${new Date().toISOString()}`);
console.log(`🌍 时区: ${process.env.TZ || 'UTC'}`);
console.log(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);

// 创建健康检查服务器
function createHealthServer() {
  const server = http.createServer((req, res) => {
    if (req.url === '/health') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        status: 'ok',
        timestamp: new Date().toISOString(),
        scheduler: { isRunning: true, isActive: true },
        uptime: process.uptime()
      }));
    } else if (req.url === '/status') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        service: 'FeedWe Crawler Service',
        version: '1.0.0',
        status: { isRunning: true, isActive: true },
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }));
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
    }
  });

  const port = process.env.PORT || 3001;
  server.listen(port, () => {
    console.log(`🏥 健康检查服务器启动在端口 ${port}`);
    console.log(`   健康检查: http://localhost:${port}/health`);
    console.log(`   状态查询: http://localhost:${port}/status`);
  });

  return server;
}

// 简化的初始化函数
function initializeApp() {
  console.log('🚀 初始化应用...');
  
  // 启动微信公众号文章定时爬取任务（默认启动）
  if (process.env.DISABLE_CRAWLER_SCHEDULER !== 'true') {
    console.log('🕐 定时爬取任务已启用');
    console.log('⚠️ 注意: 简化版本暂不包含实际的爬取逻辑');
    console.log('💡 这是一个健康检查服务，用于验证 Docker 部署');
  } else {
    console.log('⏸️ 定时爬取任务已被禁用');
    console.log('💡 如需启动，请移除环境变量: DISABLE_CRAWLER_SCHEDULER');
  }
  
  console.log('✅ 应用初始化完成');
}

// 清理函数
function cleanupApp() {
  console.log('🛑 应用关闭，清理资源...');
  console.log('✅ 资源清理完成');
}

// 主函数
async function main() {
  try {
    // 测试数据库连接
    const prisma = new PrismaClient();
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    await prisma.$disconnect();

    // 创建健康检查服务器
    const healthServer = createHealthServer();

    // 初始化应用
    initializeApp();

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`\n🛑 收到 ${signal} 信号，开始优雅关闭...`);
      
      // 关闭健康检查服务器
      healthServer.close(() => {
        console.log('🏥 健康检查服务器已关闭');
      });

      // 清理应用资源
      cleanupApp();

      // 退出进程
      setTimeout(() => {
        console.log('✅ 服务已完全关闭');
        process.exit(0);
      }, 2000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    console.log('✅ FeedWe 定时任务爬虫服务启动完成');
    console.log('🔄 服务正在运行中...');
    console.log('💡 这是简化版本，主要用于验证 Docker 部署');

  } catch (error) {
    console.error('❌ 服务启动失败:', error);
    process.exit(1);
  }
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

// 启动服务
main().catch((error) => {
  console.error('❌ 主函数执行失败:', error);
  process.exit(1);
});
