import { NextRequest, NextResponse } from 'next/server';
import {
  hashPassword,
  generateToken,
  generateInviteCode,
  validateInviteCode,
  isValidEmail,
  isValidPassword,
  AuthUser
} from '../../../lib/auth';
import { prisma } from '../../../../lib/prisma';

export async function POST(req: NextRequest) {
  try {
    const { email, password, name, inviteCode } = await req.json();

    // 验证必填字段
    if (!email || !password) {
      return NextResponse.json(
        { error: '邮箱和密码为必填项' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证密码强度
    const passwordValidation = isValidPassword(password);
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { error: passwordValidation.message },
        { status: 400 }
      );
    }

    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 400 }
      );
    }

    // 验证邀请码（如果提供）
    let inviterId: string | undefined;
    if (inviteCode) {
      const inviteValidation = await validateInviteCode(inviteCode);
      if (!inviteValidation.valid) {
        return NextResponse.json(
          { error: '邀请码无效' },
          { status: 400 }
        );
      }
      inviterId = inviteValidation.inviter?.id;
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 生成用户的邀请码
    let userInviteCode: string;
    let codeExists = true;
    
    // 确保生成的邀请码是唯一的
    while (codeExists) {
      userInviteCode = generateInviteCode();
      const existing = await prisma.user.findUnique({
        where: { inviteCode: userInviteCode }
      });
      codeExists = !!existing;
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name: name || null,
        inviteCode: userInviteCode!,
        invitedBy: inviterId || null,
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        inviteCode: true,
        createdAt: true,
      }
    });

    // 创建用于JWT的用户对象
    const userForToken: AuthUser = {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      avatar: user.avatar || undefined,
      inviteCode: user.inviteCode || undefined,
    };

    // 生成JWT token
    const token = generateToken(userForToken);

    return NextResponse.json({
      success: true,
      user,
      token,
      message: '注册成功'
    });

  } catch (error) {
    console.error('注册失败:', error);
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    );
  }
}
