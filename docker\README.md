# FeedWe 定时任务 Docker 服务

这个目录包含了 FeedWe 定时任务服务的 Docker 部署文件。

## 文件说明

- `crawler-service.js` - 定时任务服务主程序
- `start.sh` - 服务启动脚本
- `deploy.sh` - 一键部署脚本

## 快速开始

1. **配置环境变量**
   ```bash
   cp .env.crawler.example .env.crawler
   # 编辑 .env.crawler 文件
   ```

2. **部署服务**
   ```bash
   chmod +x docker/deploy.sh
   ./docker/deploy.sh
   ```

3. **检查服务状态**
   ```bash
   curl http://localhost:3001/health
   ```

## 服务特性

- ✅ 自动定时爬取微信公众号文章
- ✅ 支持多种 Webhook 通知（企业微信、钉钉、飞书）
- ✅ 健康检查和状态监控
- ✅ 优雅关闭和错误处理
- ✅ 日志记录和调试支持

## 监控端点

- `GET /health` - 健康检查
- `GET /status` - 详细状态信息

## 环境变量

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `DATABASE_URL` | ✅ | 数据库连接字符串 |
| `WECHAT_ARTICLE_COOKIE_STRING` | ✅ | 微信文章Cookie |
| `WECHAT_ARTICLE_TOKEN` | ✅ | 微信文章Token |
| `WEWORK_WEBHOOK_URL` | ❌ | 企业微信Webhook地址 |
| `DISABLE_CRAWLER_SCHEDULER` | ❌ | 是否禁用定时任务 |
| `TZ` | ❌ | 时区设置 |

详细部署说明请参考 [Docker部署指南](../docs/docker-deployment.md)。
