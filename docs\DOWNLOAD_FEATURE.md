# 微信公众号文章下载功能

## 功能概述

本功能允许用户下载已订阅的微信公众号文章，支持单篇下载和批量下载，使用代理服务器确保下载的稳定性和成功率。

## 主要特性

- ✅ 单篇文章下载
- ✅ 批量文章下载（最多50篇）
- ✅ 多代理服务器支持
- ✅ 自动重试机制
- ✅ 下载状态跟踪
- ✅ 权限验证（仅订阅用户可下载）
- ✅ HTML格式保存
- ✅ 资源完整性验证

## 数据库结构

### ProxyServer 表
存储代理服务器配置信息：
- `name`: 代理服务器名称
- `url`: 代理服务器地址
- `isActive`: 是否启用
- `priority`: 优先级（数字越小优先级越高）
- `maxRetries`: 最大重试次数
- `timeout`: 超时时间（秒）

### ArticleDownload 表
存储文章下载记录：
- `articleId`: 文章ID
- `userId`: 用户ID
- `status`: 下载状态（PENDING/DOWNLOADING/COMPLETED/FAILED）
- `filePath`: 文件路径
- `fileSize`: 文件大小
- `errorMessage`: 错误信息
- `proxyServerId`: 使用的代理服务器ID
- `downloadTime`: 下载完成时间

## API 接口

### 1. 下载单篇文章
```
POST /api/articles/download
Content-Type: application/json

{
  "articleId": "文章ID",
  "options": {
    "withCredentials": true,
    "timeout": 30
  }
}
```

### 2. 批量下载文章
```
PUT /api/articles/download
Content-Type: application/json

{
  "articleIds": ["文章ID1", "文章ID2", ...],
  "options": {
    "withCredentials": true,
    "timeout": 30
  }
}
```

### 3. 获取下载记录
```
GET /api/articles/downloads?page=1&limit=20&status=COMPLETED
```

### 4. 管理代理服务器（仅管理员）
```
GET /api/admin/proxy-servers
POST /api/admin/proxy-servers
PUT /api/admin/proxy-servers/{id}
DELETE /api/admin/proxy-servers/{id}
```

## 前端组件

### DownloadButton
单篇文章下载按钮组件：
```tsx
<DownloadButton
  articleId="文章ID"
  articleTitle="文章标题"
  variant="outline"
  size="sm"
/>
```

### BatchDownloadButton
批量下载按钮组件：
```tsx
<BatchDownloadButton
  articleIds={["文章ID1", "文章ID2"]}
  onDownloadStart={() => console.log('开始下载')}
  onDownloadComplete={() => console.log('下载完成')}
/>
```

### DownloadStatus
下载状态显示组件：
```tsx
<DownloadStatus status="COMPLETED" />
```

## 使用流程

1. **配置代理服务器**（管理员）
   - 访问 `/admin/proxy-servers` 页面
   - 添加代理服务器配置
   - 设置优先级和重试参数

2. **下载文章**（用户）
   - 在文章详情页点击下载按钮
   - 或在文章列表页选择多篇文章批量下载
   - 系统会创建下载任务并异步执行

3. **查看下载状态**
   - 访问下载记录页面查看进度
   - 支持重新下载失败的文章

## 下载逻辑

1. **权限验证**: 检查用户是否订阅了该公众号
2. **重复检查**: 避免重复下载同一篇文章
3. **代理选择**: 按优先级选择可用的代理服务器
4. **下载执行**: 使用代理下载文章HTML
5. **内容验证**: 验证下载的HTML是否完整
6. **状态更新**: 更新下载记录状态
7. **错误处理**: 记录错误信息并支持重试

## 配置说明

### 环境变量
需要在系统配置中设置微信凭据：
- `wechat_token`: 微信访问令牌
- `wechat_cookie`: 微信Cookie
- `wechat_data_ticket`: 微信数据票据

### 代理服务器格式
代理服务器应支持以下请求格式：
```
GET {proxy_url}?url={encoded_article_url}&headers={encoded_headers}
```

## 注意事项

1. **权限控制**: 只有订阅了相应公众号的用户才能下载文章
2. **频率限制**: 批次间有延迟，避免请求过于频繁
3. **文件存储**: 需要实现文件存储逻辑（本地或云存储）
4. **代理稳定性**: 建议配置多个代理服务器确保可用性
5. **错误处理**: 支持自动重试和手动重新下载

## 扩展功能

未来可以考虑添加：
- 文章内容解析和格式化
- 图片资源下载
- PDF格式导出
- 下载进度实时推送
- 下载统计和分析
