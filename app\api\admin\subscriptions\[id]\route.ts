import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';

// 更新订阅状态
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.SUBSCRIPTION_MANAGEMENT)(user);

    const { isActive } = await request.json();
    const params = await context.params;
    const subscriptionId = params.id;

    // 更新订阅状态
    const updatedSubscription = await prisma.subscription.update({
      where: { id: subscriptionId },
      data: { isActive },
      include: {
        user: {
          select: {
            email: true,
            name: true
          }
        },
        wechatAccount: {
          select: {
            name: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      subscription: updatedSubscription
    });
  } catch (error) {
    console.error('更新订阅失败:', error);
    const errorMessage = error instanceof Error ? error.message : '更新订阅失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 删除订阅
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.SUBSCRIPTION_MANAGEMENT)(user);

    const params = await context.params;
    const subscriptionId = params.id;

    // 删除订阅
    await prisma.subscription.delete({
      where: { id: subscriptionId }
    });

    return NextResponse.json({
      success: true,
      message: '订阅删除成功'
    });
  } catch (error) {
    console.error('删除订阅失败:', error);
    const errorMessage = error instanceof Error ? error.message : '删除订阅失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
