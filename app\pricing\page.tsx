'use client';

import React, { useState } from 'react';
import Navigation from '../components/Navigation';
import Link from 'next/link';

export default function PricingPage() {
  const [showContactModal, setShowContactModal] = useState(false);

  // 价格页面结构化数据
  const pricingStructuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "FeedWe微信公众号订阅工具",
    "description": "专业的微信公众号订阅管理工具，每个公众号仅需2元/月",
    "brand": {
      "@type": "Brand",
      "name": "FeedWe"
    },
    "offers": {
      "@type": "Offer",
      "price": "2",
      "priceCurrency": "CNY",
      "priceSpecification": {
        "@type": "UnitPriceSpecification",
        "price": "2",
        "priceCurrency": "CNY",
        "unitText": "月",
        "referenceQuantity": {
          "@type": "QuantitativeValue",
          "value": "1",
          "unitText": "公众号"
        }
      },
      "availability": "https://schema.org/InStock",
      "validFrom": "2024-01-01"
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <Navigation currentPage="pricing" />

      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl lg:text-5xl">
            简单透明的价格方案
          </h1>
          <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
            我们提供超值的订阅服务，让您轻松获取微信公众号内容
          </p>
        </div>

        {/* 价格卡片 - 三栏布局 */}
        <div className="mt-16 bg-white rounded-2xl shadow-xl overflow-hidden lg:grid lg:grid-cols-12">
          {/* 左侧：价格信息 */}
          <div className="px-6 py-5 lg:px-6 lg:py-5 border-r border-gray-200 lg:col-span-5">
            <h3 className="text-2xl font-extrabold text-gray-900 sm:text-3xl">
              限时特惠
            </h3>
            {/*<p className="mt-3 text-base text-gray-500">*/}
            {/*  现在订阅即可享受2折优惠价格，每个公众号每月仅需2元*/}
            {/*</p>*/}

            <h4 className="mt-5 text-lg font-semibold text-blue-600">
              核心功能
            </h4>

            <div className="mt-3 grid grid-cols-1 gap-2.5">
              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">无限量查看历史文章</span>
              </div>

              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">新文章实时推送通知</span>
              </div>

              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">企业微信/钉钉/飞书推送</span>
              </div>

              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">一键导出Excel数据</span>
              </div>

              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">文章单篇/批量下载到本地</span>
              </div>

              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">7×24小时技术支持</span>
              </div>

              <div className="flex items-center">
                <svg className="flex-shrink-0 w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="ml-3 text-base text-gray-700">邀请返现50%佣金</span>
              </div>
            </div>

            <h4 className="mt-5 text-lg font-semibold text-blue-600">
              价格详情
            </h4>

            <div className="mt-3">
              <div className="flex items-baseline text-6xl font-extrabold">
                ¥2
                <span className="ml-1 text-2xl font-medium text-gray-500">
                  每个公众号 / 每月
                </span>
              </div>
              <p className="text-sm text-gray-500">
                原价 ¥10 
                <span className="ml-2 bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">
                  2折
                </span>
              </p>
            </div>

            <div className="mt-5">
              <div className="rounded-md shadow">
                <Link href="/subscriptions" className="block w-full text-center rounded-lg border border-transparent bg-indigo-600 px-6 py-4 text-xl leading-6 font-medium text-white hover:bg-indigo-700 focus:outline-none transition duration-150 ease-in-out">
                  立即订阅
                </Link>
              </div>
            </div>
          </div>

          {/* 中间：联系我们 */}
          <div className="py-6 px-6 text-center bg-gray-50 border-r border-gray-200 lg:flex lg:flex-col lg:justify-center lg:py-6 lg:px-4 lg:col-span-3">
            <p className="text-lg font-medium text-gray-800">联系我们</p>
            <div className="mt-4">
              <div className="rounded-md shadow">
                <div className="flex flex-col items-center justify-center">
                  <img
                    src="/images/wechat-qrcode.png"
                    alt="微信二维码"
                    className="w-36 h-36 object-cover rounded-lg shadow-md"
                  />
                  <p className="mt-2 text-sm text-gray-600">
                    扫描上方二维码添加微信
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    或添加微信号: SilenceIsLife
                  </p>
                </div>
              </div>
              <p className="mt-3 text-center text-sm text-gray-500">
                有任何问题或需求，欢迎随时联系我们
              </p>
            </div>
          </div>

          {/* 右侧：定制开发 */}
          <div className="py-4 px-6 lg:py-4 lg:px-4 lg:flex lg:flex-col lg:justify-start lg:col-span-4">
            <div className="p-4 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg border border-purple-100">
              <div className="text-center">
                <svg className="w-10 h-10 text-purple-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z" />
                </svg>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  需要定制开发？
                </h3>
                <p className="text-base text-gray-600 mb-3 leading-relaxed">
                  我们提供专业的定制开发服务，满足您的个性化需求
                </p>

                <div className="space-y-4 text-sm text-gray-600 mb-3">
            
                  <div className="flex items-start text-left">
                    <svg className="w-4 h-4 text-purple-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <div className="font-semibold text-gray-800">专属功能定制开发</div>
                      <div className="text-gray-500 mt-1 leading-relaxed">根据业务需求量身定制</div>
                    </div>
                  </div>
                  <div className="flex items-start text-left">
                    <svg className="w-4 h-4 text-purple-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <div className="font-semibold text-gray-800">全程技术支持服务</div>
                      <div className="text-gray-500 mt-1 leading-relaxed">专业团队7×24小时支持</div>
                    </div>
                  </div>
                  <div className="flex items-start text-left">
                    <svg className="w-4 h-4 text-purple-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <div className="font-semibold text-gray-800">API接口集成</div>
                      <div className="text-gray-500 mt-1 leading-relaxed">与现有系统无缝对接</div>
                    </div>
                  </div>
                  <div className="flex items-start text-left">
                    <svg className="w-4 h-4 text-purple-500 mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <div className="font-semibold text-gray-800">数据分析报表</div>
                      <div className="text-gray-500 mt-1 leading-relaxed">深度数据挖掘和可视化</div>
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t border-purple-200">
                  <p className="text-sm text-gray-500 mb-3 font-medium">联系我们获取定制方案</p>
                  <button
                    onClick={() => setShowContactModal(true)}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white text-base font-semibold py-3 px-4 rounded-lg transition-colors mb-3"
                  >
                    联系我们
                  </button>
                  <div className="flex justify-center space-x-2">
                    <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                      快速响应
                    </span>
                    <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                      专业团队
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 常见问题 */}
        <div className="mt-16">
          <h2 className="text-2xl font-extrabold text-gray-900 text-center">
            常见问题
          </h2>
          <div className="mt-12">
            <dl className="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-12">
              <div>
                <dt className="text-lg leading-6 font-medium text-gray-900">
                  如何开始使用？
                </dt>
                <dd className="mt-2 text-base text-gray-500">
                  注册账户后，在订阅管理页面添加您想要关注的微信公众号，即可开始查看历史文章和接收新文章推送。
                </dd>
              </div>

              <div>
                <dt className="text-lg leading-6 font-medium text-gray-900">
                  支持哪些推送平台？
                </dt>
                <dd className="mt-2 text-base text-gray-500">
                  目前支持企业微信、钉钉、飞书三大主流办公平台的消息推送，让您第一时间获取重要内容。
                </dd>
              </div>

              <div>
                <dt className="text-lg leading-6 font-medium text-gray-900">
                  数据导出功能如何使用？
                </dt>
                <dd className="mt-2 text-base text-gray-500">
                  在文章列表页面点击"导出Excel"按钮，可以将文章标题、链接、发布时间、作者等信息导出为Excel文件。
                </dd>
              </div>

              <div>
                <dt className="text-lg leading-6 font-medium text-gray-900">
                  邀请返现如何获得？
                </dt>
                <dd className="mt-2 text-base text-gray-500">
                  在"邀请"页面获取您的专属邀请链接，当新用户通过您的链接注册并消费时，您将获得其首月消费金额的50%作为返现奖励。
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* 联系我们弹窗 */}
      {showContactModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
            {/* 关闭按钮 */}
            <button
              onClick={() => setShowContactModal(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* 弹窗内容 */}
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                联系我们
              </h3>
              <p className="text-gray-600 mb-6">
                扫描下方二维码添加微信，获取定制开发方案
              </p>

              <div className="flex justify-center mb-4">
                <img
                  src="/images/wechat-qrcode.png"
                  alt="微信二维码"
                  className="w-48 h-48 object-cover rounded-lg shadow-md"
                />
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">
                  扫描上方二维码添加微信
                </p>
                <p className="text-xs text-gray-500 mb-4">
                  或添加微信号: SilenceIsLife
                </p>

                <div className="bg-purple-50 rounded-lg p-3">
                  <p className="text-sm text-purple-800 font-medium mb-1">
                    🚀 专业定制开发服务
                  </p>
                  <p className="text-xs text-purple-600">
                    企业级解决方案 • 7×24小时技术支持 • 快速响应
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 价格页面结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(pricingStructuredData)
        }}
      />
    </div>
  );
}
