import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/auth/jwt';
import { requireAdmin } from '../../../../lib/auth/admin';
import { prisma } from '../../../../lib/prisma';

// 定义活动类型
interface Activity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  user: string;
}

// 获取真实的最近活动
async function getRecentActivity(): Promise<Activity[]> {
  const activities: Activity[] = [];

  // 获取最近的用户注册
  const recentUsers = await prisma.user.findMany({
    orderBy: { createdAt: 'desc' },
    take: 10,
    select: {
      id: true,
      email: true,
      name: true,
      createdAt: true
    }
  });

  // 获取最近的订阅活动
  const recentSubscriptions = await prisma.subscription.findMany({
    orderBy: { createdAt: 'desc' },
    take: 10,
    include: {
      user: {
        select: {
          email: true,
          name: true
        }
      },
      wechatAccount: {
        select: {
          name: true
        }
      }
    }
  });

  // 转换用户注册活动
  recentUsers.forEach(user => {
    activities.push({
      id: `user_${user.id}`,
      type: 'user_register',
      description: `新用户注册: ${user.name || user.email}`,
      timestamp: user.createdAt.toISOString(),
      user: user.email
    });
  });

  // 转换订阅活动
  recentSubscriptions.forEach(subscription => {
    activities.push({
      id: `sub_${subscription.id}`,
      type: 'subscription_add',
      description: `用户订阅了"${subscription.wechatAccount.name}"`,
      timestamp: subscription.createdAt.toISOString(),
      user: subscription.user.email
    });
  });

  // 按时间排序并返回最近的20条
  return activities
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 20);
}

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin()(user);

    // 获取最近活动
    const activities = await getRecentActivity();

    return NextResponse.json(activities);
  } catch (error) {
    console.error('获取活动记录失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取活动记录失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
