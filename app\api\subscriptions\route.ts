import { NextRequest, NextResponse } from 'next/server';
import { getUserFromRequest } from '../../lib/auth';
import { prisma } from '../../../lib/prisma';

// 获取用户订阅列表
export async function GET(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const subscriptions = await prisma.subscription.findMany({
      where: { userId: user.id },
      include: {
        wechatAccount: true
      },
      orderBy: { createdAt: 'desc' }
    });

    // 处理微信图片URL，使用代理
    const processedSubscriptions = subscriptions.map(subscription => ({
      ...subscription,
      wechatAccount: {
        ...subscription.wechatAccount,
        avatar: subscription.wechatAccount.avatar && (
          subscription.wechatAccount.avatar.includes('mmbiz.qpic.cn') ||
          subscription.wechatAccount.avatar.includes('mmbiz.qlogo.cn') ||
          subscription.wechatAccount.avatar.includes('wx.qlogo.cn') ||
          subscription.wechatAccount.avatar.includes('thirdwx.qlogo.cn')
        ) ? `/api/proxy/wechat-image?url=${encodeURIComponent(subscription.wechatAccount.avatar)}`
          : subscription.wechatAccount.avatar
      }
    }));

    return NextResponse.json(processedSubscriptions);
  } catch (error) {
    console.error('获取订阅列表失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// 创建订阅
export async function POST(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { wechatAccountId } = await req.json();
    if (!wechatAccountId) {
      return NextResponse.json({ error: 'wechatAccountId required' }, { status: 400 });
    }

    console.log('📝 创建订阅:', { userId: user.id, wechatAccountId });

    // 1. 校验公众号是否存在
    const wechatAccount = await prisma.wechatAccount.findUnique({
      where: { id: wechatAccountId }
    });

    if (!wechatAccount) {
      return NextResponse.json({ error: '公众号不存在' }, { status: 404 });
    }

    // 2. 创建或更新订阅
    const subscription = await prisma.subscription.upsert({
      where: {
        userId_wechatAccountId: {
          userId: user.id,
          wechatAccountId: wechatAccountId
        }
      },
      update: {
        isActive: true,
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        wechatAccountId: wechatAccountId,
        isActive: true
      },
      include: {
        wechatAccount: true
      }
    });

    console.log('✅ 订阅创建成功:', subscription.id);

    return NextResponse.json(subscription);
  } catch (error) {
    console.error('创建订阅失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// 取消订阅
export async function DELETE(req: NextRequest) {
  try {
    const user = await getUserFromRequest(req);
    if (!user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const { wechatAccountId } = await req.json();
    if (!wechatAccountId) {
      return NextResponse.json({ error: 'wechatAccountId required' }, { status: 400 });
    }

    console.log('🗑️ 取消订阅:', { userId: user.id, wechatAccountId });

    // 删除订阅或设置为不活跃
    const subscription = await prisma.subscription.updateMany({
      where: {
        userId: user.id,
        wechatAccountId: wechatAccountId
      },
      data: {
        isActive: false,
        updatedAt: new Date()
      }
    });

    if (subscription.count === 0) {
      return NextResponse.json({ error: '订阅不存在' }, { status: 404 });
    }

    console.log('✅ 订阅取消成功');

    return NextResponse.json({ success: true, message: '订阅已取消' });
  } catch (error) {
    console.error('取消订阅失败:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}