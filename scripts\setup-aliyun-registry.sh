#!/bin/bash

# 阿里云镜像仓库设置脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🚀 阿里云镜像仓库设置向导"
echo ""

# 检查是否已登录阿里云
print_message $BLUE "📋 请提供以下信息："
echo ""

# 获取用户输入
read -p "阿里云镜像仓库地域 (默认: cn-hangzhou): " REGION
REGION=${REGION:-cn-hangzhou}

read -p "阿里云镜像仓库命名空间 (默认: feedwe): " NAMESPACE
NAMESPACE=${NAMESPACE:-feedwe}

read -p "镜像名称 (默认: crawler): " IMAGE_NAME
IMAGE_NAME=${IMAGE_NAME:-crawler}

read -p "阿里云镜像仓库用户名: " USERNAME
if [ -z "$USERNAME" ]; then
    print_message $RED "❌ 用户名不能为空"
    exit 1
fi

read -s -p "阿里云镜像仓库密码: " PASSWORD
echo ""
if [ -z "$PASSWORD" ]; then
    print_message $RED "❌ 密码不能为空"
    exit 1
fi

# 构建镜像仓库地址
REGISTRY="registry.${REGION}.aliyuncs.com"
FULL_IMAGE_NAME="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}"

print_message $BLUE "🔧 配置信息："
echo "  镜像仓库: $REGISTRY"
echo "  命名空间: $NAMESPACE"
echo "  镜像名称: $IMAGE_NAME"
echo "  完整地址: $FULL_IMAGE_NAME"
echo ""

# 测试登录
print_message $BLUE "🔐 测试登录阿里云镜像仓库..."
if echo "$PASSWORD" | docker login --username "$USERNAME" --password-stdin "$REGISTRY"; then
    print_message $GREEN "✅ 登录成功"
else
    print_message $RED "❌ 登录失败，请检查用户名和密码"
    exit 1
fi

# 创建 GitHub Secrets 设置说明
print_message $BLUE "📝 GitHub Secrets 设置说明："
echo ""
print_message $YELLOW "请在 GitHub 仓库的 Settings > Secrets and variables > Actions 中添加以下 Secrets："
echo ""
echo "ALIYUN_REGISTRY_USERNAME: $USERNAME"
echo "ALIYUN_REGISTRY_PASSWORD: [您的密码]"
echo ""

# 更新 docker-compose 文件
print_message $BLUE "🔄 更新 docker-compose.crawler.yml..."
if [ -f "docker-compose.crawler.yml" ]; then
    # 备份原文件
    cp docker-compose.crawler.yml docker-compose.crawler.yml.backup
    
    # 更新镜像地址
    sed -i.tmp "s|image:.*|image: $FULL_IMAGE_NAME:latest|g" docker-compose.crawler.yml
    rm -f docker-compose.crawler.yml.tmp
    
    print_message $GREEN "✅ docker-compose.crawler.yml 已更新"
else
    print_message $YELLOW "⚠️ docker-compose.crawler.yml 文件不存在"
fi

# 更新 GitHub Actions 工作流
print_message $BLUE "🔄 更新 GitHub Actions 配置..."
if [ -f ".github/workflows/docker-build.yml" ]; then
    # 备份原文件
    cp .github/workflows/docker-build.yml .github/workflows/docker-build.yml.backup
    
    # 更新配置
    sed -i.tmp "s|REGISTRY:.*|REGISTRY: $REGISTRY|g" .github/workflows/docker-build.yml
    sed -i.tmp "s|NAMESPACE:.*|NAMESPACE: $NAMESPACE|g" .github/workflows/docker-build.yml
    sed -i.tmp "s|IMAGE_NAME:.*|IMAGE_NAME: $IMAGE_NAME|g" .github/workflows/docker-build.yml
    rm -f .github/workflows/docker-build.yml.tmp
    
    print_message $GREEN "✅ GitHub Actions 配置已更新"
else
    print_message $YELLOW "⚠️ GitHub Actions 配置文件不存在"
fi

# 创建本地构建和推送脚本
print_message $BLUE "📝 创建本地构建脚本..."
cat > scripts/build-and-push.sh << EOF
#!/bin/bash

# 本地构建和推送脚本

set -e

REGISTRY="$REGISTRY"
NAMESPACE="$NAMESPACE"
IMAGE_NAME="$IMAGE_NAME"
TAG=\${1:-latest}

FULL_IMAGE_NAME="\${REGISTRY}/\${NAMESPACE}/\${IMAGE_NAME}:\${TAG}"

echo "🔨 构建 Docker 镜像: \$FULL_IMAGE_NAME"
docker build -f Dockerfile.crawler -t "\$FULL_IMAGE_NAME" .

echo "📤 推送镜像到阿里云..."
docker push "\$FULL_IMAGE_NAME"

echo "✅ 构建和推送完成: \$FULL_IMAGE_NAME"
EOF

chmod +x scripts/build-and-push.sh
print_message $GREEN "✅ 本地构建脚本已创建: scripts/build-and-push.sh"

# 创建部署脚本
print_message $BLUE "📝 创建部署脚本..."
cat > scripts/deploy-from-registry.sh << EOF
#!/bin/bash

# 从镜像仓库部署脚本

set -e

TAG=\${1:-latest}
COMPOSE_FILE=\${2:-docker-compose.crawler.yml}

echo "🚀 从镜像仓库部署 FeedWe Crawler..."
echo "镜像标签: \$TAG"
echo "Compose 文件: \$COMPOSE_FILE"

# 更新镜像标签
sed -i.tmp "s|image:.*|image: $FULL_IMAGE_NAME:\$TAG|g" "\$COMPOSE_FILE"
rm -f "\$COMPOSE_FILE.tmp"

# 拉取最新镜像
docker-compose -f "\$COMPOSE_FILE" pull

# 重新部署
docker-compose -f "\$COMPOSE_FILE" up -d

echo "✅ 部署完成"
EOF

chmod +x scripts/deploy-from-registry.sh
print_message $GREEN "✅ 部署脚本已创建: scripts/deploy-from-registry.sh"

print_message $GREEN "🎉 阿里云镜像仓库设置完成！"
echo ""
print_message $BLUE "📋 后续步骤："
echo "1. 在 GitHub 仓库中设置 Secrets"
echo "2. 推送代码触发自动构建"
echo "3. 使用 scripts/deploy-from-registry.sh 部署"
echo ""
print_message $BLUE "🔗 有用的命令："
echo "  本地构建: ./scripts/build-and-push.sh [tag]"
echo "  从仓库部署: ./scripts/deploy-from-registry.sh [tag]"
echo "  查看镜像: docker images | grep $NAMESPACE"
