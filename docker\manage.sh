#!/bin/bash

# FeedWe 定时任务服务管理脚本

set -e

COMPOSE_FILE="docker-compose.crawler.yml"
SERVICE_NAME="feedwe-crawler"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "FeedWe 定时任务服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  health    检查服务健康状态"
    echo "  deploy    部署/更新服务"
    echo "  clean     清理服务和数据"
    echo "  backup    备份配置和日志"
    echo "  help      显示此帮助信息"
    echo ""
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "❌ Docker 未安装"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "❌ Docker Compose 未安装"
        exit 1
    fi
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env.crawler" ]; then
        print_message $RED "❌ 环境变量文件 .env.crawler 不存在"
        print_message $YELLOW "请复制 .env.crawler.example 为 .env.crawler 并填写配置"
        exit 1
    fi
}

# 启动服务
start_service() {
    print_message $BLUE "🚀 启动 FeedWe 定时任务服务..."
    check_requirements
    check_env_file
    
    docker-compose -f $COMPOSE_FILE up -d
    
    print_message $GREEN "✅ 服务启动完成"
    print_message $BLUE "🔍 等待服务就绪..."
    sleep 5
    check_health
}

# 停止服务
stop_service() {
    print_message $BLUE "🛑 停止 FeedWe 定时任务服务..."
    docker-compose -f $COMPOSE_FILE down
    print_message $GREEN "✅ 服务已停止"
}

# 重启服务
restart_service() {
    print_message $BLUE "🔄 重启 FeedWe 定时任务服务..."
    docker-compose -f $COMPOSE_FILE restart
    print_message $GREEN "✅ 服务重启完成"
    sleep 5
    check_health
}

# 查看服务状态
show_status() {
    print_message $BLUE "📊 FeedWe 定时任务服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    print_message $BLUE "📈 容器资源使用情况:"
    docker stats $SERVICE_NAME --no-stream 2>/dev/null || print_message $YELLOW "⚠️ 服务未运行"
}

# 查看服务日志
show_logs() {
    print_message $BLUE "📋 FeedWe 定时任务服务日志:"
    docker-compose -f $COMPOSE_FILE logs -f --tail=100
}

# 检查服务健康状态
check_health() {
    print_message $BLUE "🏥 检查服务健康状态..."
    
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        print_message $GREEN "✅ 服务健康检查通过"
        
        # 获取详细状态
        echo ""
        print_message $BLUE "📊 服务详细状态:"
        curl -s http://localhost:3001/status | python3 -m json.tool 2>/dev/null || curl -s http://localhost:3001/status
    else
        print_message $RED "❌ 服务健康检查失败"
        print_message $YELLOW "请检查服务日志: $0 logs"
        return 1
    fi
}

# 部署/更新服务
deploy_service() {
    print_message $BLUE "🚀 部署/更新 FeedWe 定时任务服务..."
    check_requirements
    check_env_file
    
    # 停止现有服务
    docker-compose -f $COMPOSE_FILE down || true
    
    # 构建并启动服务
    docker-compose -f $COMPOSE_FILE up -d --build
    
    print_message $BLUE "⏳ 等待服务启动..."
    sleep 10
    
    if check_health; then
        print_message $GREEN "🎉 部署成功！"
        print_message $BLUE "🌐 健康检查: http://localhost:3001/health"
        print_message $BLUE "📊 状态查询: http://localhost:3001/status"
    else
        print_message $RED "❌ 部署失败，请检查日志"
        show_logs
        exit 1
    fi
}

# 清理服务和数据
clean_service() {
    print_message $YELLOW "⚠️ 这将删除服务容器和相关数据，是否继续？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message $BLUE "🧹 清理 FeedWe 定时任务服务..."
        docker-compose -f $COMPOSE_FILE down -v --rmi all
        print_message $GREEN "✅ 清理完成"
    else
        print_message $BLUE "取消清理操作"
    fi
}

# 备份配置和日志
backup_service() {
    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    print_message $BLUE "💾 备份配置和日志到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份环境变量文件
    if [ -f ".env.crawler" ]; then
        cp .env.crawler "$backup_dir/"
        print_message $GREEN "✅ 环境变量文件已备份"
    fi
    
    # 备份日志
    if [ -d "logs" ]; then
        cp -r logs "$backup_dir/"
        print_message $GREEN "✅ 日志文件已备份"
    fi
    
    # 导出容器日志
    if docker ps | grep -q $SERVICE_NAME; then
        docker-compose -f $COMPOSE_FILE logs > "$backup_dir/container.log"
        print_message $GREEN "✅ 容器日志已导出"
    fi
    
    print_message $GREEN "🎉 备份完成: $backup_dir"
}

# 主逻辑
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    health)
        check_health
        ;;
    deploy)
        deploy_service
        ;;
    clean)
        clean_service
        ;;
    backup)
        backup_service
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
