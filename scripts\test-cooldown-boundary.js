/**
 * 测试30分钟冷却期的边界情况
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testCooldownBoundary() {
  try {
    console.log('🧪 测试30分钟冷却期边界情况...\n');

    // 获取一个测试公众号
    let testAccount = await prisma.wechatAccount.findFirst({
      where: {
        enableCrawling: true,
        fakeid: { not: null }
      }
    });

    if (!testAccount) {
      console.log('❌ 未找到可测试的公众号');
      return;
    }

    console.log(`📋 使用测试公众号: ${testAccount.name}\n`);

    // 备份现有文章
    const existingArticles = await prisma.article.findMany({
      where: { wechatAccountId: testAccount.id }
    });
    console.log(`📦 备份了 ${existingArticles.length} 篇现有文章\n`);

    // 清理现有文章以确保测试准确性
    if (existingArticles.length > 0) {
      await prisma.article.deleteMany({
        where: { wechatAccountId: testAccount.id }
      });
    }

    // 测试场景1: 创建29分钟前的文章（应该被跳过）
    console.log('🔬 测试场景1: 29分钟前的文章（应该被跳过）');
    const article29min = await createTestArticle(testAccount.id, 29);

    if (article29min) {
      const shouldSkip = await checkIfAccountShouldSkip(testAccount.id);
      console.log(`结果: ${shouldSkip ? '✅ 正确跳过' : '❌ 错误执行'}`);

      // 清理测试数据
      await prisma.article.delete({ where: { id: article29min.id } });
    }

    console.log('');

    // 测试场景2: 创建31分钟前的文章（应该可以执行）
    console.log('🔬 测试场景2: 31分钟前的文章（应该可以执行）');
    const article31min = await createTestArticle(testAccount.id, 31);
    
    if (article31min) {
      const shouldSkip = await checkIfAccountShouldSkip(testAccount.id);
      console.log(`结果: ${shouldSkip ? '❌ 错误跳过' : '✅ 正确执行'}`);
      
      // 清理测试数据
      await prisma.article.delete({ where: { id: article31min.id } });
    }

    console.log('');

    // 测试场景3: 恰好30分钟前的文章（边界情况）
    console.log('🔬 测试场景3: 恰好30分钟前的文章（边界情况）');
    const article30min = await createTestArticle(testAccount.id, 30);
    
    if (article30min) {
      const shouldSkip = await checkIfAccountShouldSkip(testAccount.id);
      console.log(`结果: ${shouldSkip ? '⚠️ 被跳过（边界处理）' : '✅ 可以执行（边界处理）'}`);
      
      // 清理测试数据
      await prisma.article.delete({ where: { id: article30min.id } });
    }

    console.log('');

    // 测试场景4: 没有文章的公众号（应该可以执行）
    console.log('🔬 测试场景4: 没有文章的公众号（应该可以执行）');
    
    // 临时删除所有文章来模拟没有文章的情况
    const existingArticles = await prisma.article.findMany({
      where: { wechatAccountId: testAccount.id }
    });
    
    if (existingArticles.length > 0) {
      await prisma.article.deleteMany({
        where: { wechatAccountId: testAccount.id }
      });
    }
    
    const shouldSkipNoArticles = await checkIfAccountShouldSkip(testAccount.id);
    console.log(`结果: ${shouldSkipNoArticles ? '❌ 错误跳过' : '✅ 正确执行'}`);

    console.log('\n🏁 边界测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 创建测试文章
async function createTestArticle(accountId, minutesAgo) {
  const publishDate = new Date(Date.now() - minutesAgo * 60 * 1000);
  
  try {
    const article = await prisma.article.create({
      data: {
        title: `边界测试文章 - ${minutesAgo}分钟前`,
        url: `https://mp.weixin.qq.com/s/boundary_test_${Date.now()}`,
        summary: `这是一篇用于测试${minutesAgo}分钟冷却期边界的文章`,
        publishDate: publishDate,
        wechatAccountId: accountId
      }
    });
    
    console.log(`  📝 创建测试文章: ${article.title}`);
    console.log(`  📅 发布时间: ${publishDate.toLocaleString()}`);
    return article;
  } catch (error) {
    console.error('  ❌ 创建测试文章失败:', error);
    return null;
  }
}

// 检查公众号是否应该被跳过（模拟定时任务逻辑）
async function checkIfAccountShouldSkip(accountId) {
  try {
    // 获取公众号及其最新文章
    const account = await prisma.wechatAccount.findUnique({
      where: { id: accountId },
      include: {
        articles: {
          orderBy: {
            publishDate: 'desc'
          },
          take: 1
        }
      }
    });

    if (!account) {
      return true; // 公众号不存在，跳过
    }

    // 如果没有文章，不跳过
    if (account.articles.length === 0) {
      console.log(`  📊 没有文章，可以执行`);
      return false;
    }
    
    // 检查最新文章的发布时间
    const latestArticle = account.articles[0];
    const timeDiff = Date.now() - latestArticle.publishDate.getTime();
    const minutesAgo = Math.floor(timeDiff / (1000 * 60));
    
    console.log(`  📊 最新文章发布于 ${minutesAgo} 分钟前`);
    
    // 如果最新文章发布时间超过30分钟，不跳过
    const shouldSkip = minutesAgo < 30;
    return shouldSkip;
    
  } catch (error) {
    console.error('  ❌ 检查失败:', error);
    return true; // 出错时跳过
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testCooldownBoundary().catch(console.error);
}

module.exports = { testCooldownBoundary };
