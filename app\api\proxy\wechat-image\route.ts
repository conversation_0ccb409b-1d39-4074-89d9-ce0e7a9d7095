import { NextRequest, NextResponse } from 'next/server';

/**
 * 微信图片代理接口
 * 解决微信图片防盗链问题
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return NextResponse.json(
        { error: '缺少图片URL参数' },
        { status: 400 }
      );
    }

    // 验证是否为微信域名的图片
    const allowedDomains = [
      'mmbiz.qpic.cn',
      'mmbiz.qlogo.cn', 
      'wx.qlogo.cn',
      'thirdwx.qlogo.cn',
      'mp.weixin.qq.com'
    ];

    let isWechatImage = false;
    try {
      const url = new URL(imageUrl);
      isWechatImage = allowedDomains.some(domain => 
        url.hostname === domain || url.hostname.endsWith('.' + domain)
      );
    } catch (e) {
      return NextResponse.json(
        { error: '无效的图片URL' },
        { status: 400 }
      );
    }

    if (!isWechatImage) {
      return NextResponse.json(
        { error: '只支持微信域名的图片' },
        { status: 400 }
      );
    }

    console.log('🖼️ 代理微信图片:', imageUrl);

    // 请求微信图片，伪装成微信域名的请求
    const response = await fetch(imageUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2307260 MicroMessenger/8.0.5 Language/zh_CN webview/',
        'Referer': 'https://mp.weixin.qq.com/',
        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      console.error('获取微信图片失败:', response.status, response.statusText);
      return NextResponse.json(
        { error: `获取图片失败: ${response.status}` },
        { status: response.status }
      );
    }

    // 获取图片数据
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    console.log('✅ 成功代理微信图片, 大小:', imageBuffer.byteLength, 'bytes');

    // 返回图片数据，设置适当的缓存头
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // 缓存1天
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    });

  } catch (error) {
    console.error('图片代理失败:', error);
    return NextResponse.json(
      { error: '图片代理失败' },
      { status: 500 }
    );
  }
}

// 支持OPTIONS请求（CORS预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
