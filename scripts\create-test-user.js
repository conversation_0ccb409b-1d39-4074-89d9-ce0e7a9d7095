const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('👤 创建测试用户...');

    const testUsers = [
      {
        email: '<EMAIL>',
        name: '张三',
        password: '123456'
      },
      {
        email: '<EMAIL>',
        name: '李四',
        password: '123456'
      },
      {
        email: '<EMAIL>',
        name: '王五',
        password: '123456'
      }
    ];

    for (const userData of testUsers) {
      // 检查用户是否已存在
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });

      if (!existingUser) {
        // 加密密码
        const hashedPassword = await bcrypt.hash(userData.password, 12);

        // 创建用户
        const user = await prisma.user.create({
          data: {
            email: userData.email,
            name: userData.name,
            password: hashedPassword,
            role: 'USER',
            isActive: true
          }
        });

        console.log(`✅ 创建用户: ${userData.name} (${userData.email})`);
      } else {
        console.log(`⚠️ 用户已存在: ${userData.email}`);
      }
    }

    console.log('🎉 测试用户创建完成！');

    // 显示统计信息
    const totalUsers = await prisma.user.count();
    console.log(`📊 当前用户总数: ${totalUsers}`);

  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
