import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('📊 数据库统计信息\n');

  // 用户统计
  const userCount = await prisma.user.count();
  console.log(`👥 用户总数: ${userCount}`);

  // 公众号统计
  const accountCount = await prisma.wechatAccount.count();
  const accounts = await prisma.wechatAccount.findMany({
    select: {
      name: true,
      _count: {
        select: {
          articles: true,
          subscriptions: true
        }
      }
    }
  });

  console.log(`\n📱 公众号总数: ${accountCount}`);
  console.log('公众号详情:');
  accounts.forEach(account => {
    console.log(`  • ${account.name}: ${account._count.articles} 篇文章, ${account._count.subscriptions} 个订阅`);
  });

  // 文章统计
  const articleCount = await prisma.article.count();
  const totalReadCount = await prisma.article.aggregate({
    _sum: {
      readCount: true,
      likeCount: true
    }
  });

  console.log(`\n📰 文章总数: ${articleCount}`);
  console.log(`📖 总阅读数: ${totalReadCount._sum.readCount?.toLocaleString() || 0}`);
  console.log(`👍 总点赞数: ${totalReadCount._sum.likeCount?.toLocaleString() || 0}`);

  // 订阅统计
  const subscriptionCount = await prisma.subscription.count();
  const subscriptions = await prisma.subscription.findMany({
    include: {
      user: {
        select: { name: true }
      },
      wechatAccount: {
        select: { name: true }
      }
    }
  });

  console.log(`\n🔔 订阅总数: ${subscriptionCount}`);
  console.log('订阅详情:');
  subscriptions.forEach(sub => {
    console.log(`  • ${sub.user.name || '未知用户'} 订阅了 ${sub.wechatAccount.name}`);
  });

  // 最新文章
  const latestArticles = await prisma.article.findMany({
    take: 5,
    orderBy: { publishDate: 'desc' },
    include: {
      wechatAccount: {
        select: { name: true }
      }
    }
  });

  console.log('\n🆕 最新文章 (前5篇):');
  latestArticles.forEach((article, index) => {
    const date = article.publishDate.toLocaleDateString('zh-CN');
    console.log(`  ${index + 1}. ${article.title}`);
    console.log(`     来源: ${article.wechatAccount.name} | 发布: ${date} | 阅读: ${article.readCount?.toLocaleString()}`);
  });

  // 热门文章
  const popularArticles = await prisma.article.findMany({
    take: 5,
    orderBy: { readCount: 'desc' },
    include: {
      wechatAccount: {
        select: { name: true }
      }
    }
  });

  console.log('\n🔥 热门文章 (前5篇):');
  popularArticles.forEach((article, index) => {
    console.log(`  ${index + 1}. ${article.title}`);
    console.log(`     来源: ${article.wechatAccount.name} | 阅读: ${article.readCount?.toLocaleString()} | 点赞: ${article.likeCount?.toLocaleString()}`);
  });

  console.log('\n✅ 统计完成！');
}

main()
  .catch((e) => {
    console.error('❌ 统计失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
