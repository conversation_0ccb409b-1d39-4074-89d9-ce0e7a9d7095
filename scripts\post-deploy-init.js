/**
 * 部署后自动初始化脚本
 * 确保定时任务在部署后自动启动
 */

const https = require('https');
const http = require('http');

// 获取应用URL
const APP_URL = process.env.VERCEL_URL 
  ? `https://${process.env.VERCEL_URL}`
  : process.env.APP_URL 
  ? process.env.APP_URL
  : 'http://localhost:3000';

console.log('🚀 部署后初始化脚本启动...');
console.log(`📡 目标URL: ${APP_URL}`);

/**
 * 发送HTTP请求
 */
function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const options = {
      method: method,
      timeout: 30000, // 30秒超时
      headers: {
        'User-Agent': 'FeedWe-PostDeploy-Script/1.0'
      }
    };

    const req = client.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.end();
  });
}

/**
 * 等待应用启动
 */
async function waitForApp(maxRetries = 10) {
  console.log('⏳ 等待应用启动...');
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await makeRequest(`${APP_URL}/api/init`);
      
      if (response.statusCode === 200) {
        console.log('✅ 应用已启动');
        return response.data;
      }
      
      console.log(`⏳ 第 ${i + 1} 次检查，状态码: ${response.statusCode}`);
    } catch (error) {
      console.log(`⏳ 第 ${i + 1} 次检查失败: ${error.message}`);
    }
    
    // 等待5秒后重试
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  throw new Error('应用启动超时');
}

/**
 * 触发初始化
 */
async function triggerInit() {
  console.log('🔄 触发应用初始化...');
  
  try {
    const response = await makeRequest(`${APP_URL}/api/init`, 'POST');
    
    if (response.statusCode === 200) {
      console.log('✅ 初始化成功');
      console.log('📊 初始化结果:', JSON.stringify(response.data, null, 2));
      return response.data;
    } else {
      console.log('❌ 初始化失败，状态码:', response.statusCode);
      console.log('📄 响应内容:', response.data);
      return null;
    }
  } catch (error) {
    console.error('❌ 初始化请求失败:', error.message);
    return null;
  }
}

/**
 * 检查定时任务状态
 */
async function checkSchedulerStatus() {
  console.log('🔍 检查定时任务状态...');
  
  try {
    const response = await makeRequest(`${APP_URL}/api/init`);
    
    if (response.statusCode === 200 && response.data.schedulerStatus) {
      const status = response.data.schedulerStatus;
      console.log('📊 定时任务状态:');
      console.log(`  - 运行中: ${status.isRunning ? '✅' : '❌'}`);
      console.log(`  - 连续失败次数: ${status.consecutiveFailures}/${status.maxConsecutiveFailures}`);
      console.log(`  - 在允许时间内: ${status.isInAllowedTime ? '✅' : '❌'}`);
      
      if (status.nextExecution) {
        console.log(`  - 下次执行: ${new Date(status.nextExecution).toLocaleString()}`);
      }
      
      return status.isRunning;
    } else {
      console.log('❌ 无法获取定时任务状态');
      return false;
    }
  } catch (error) {
    console.error('❌ 检查状态失败:', error.message);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 1. 等待应用启动
    await waitForApp();
    
    // 2. 触发初始化
    const initResult = await triggerInit();
    
    // 3. 检查定时任务状态
    const isSchedulerRunning = await checkSchedulerStatus();
    
    if (isSchedulerRunning) {
      console.log('🎉 部署后初始化完成！定时任务已启动');
      process.exit(0);
    } else {
      console.log('⚠️ 定时任务未启动，可能需要手动检查');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 部署后初始化失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { main, waitForApp, triggerInit, checkSchedulerStatus };
