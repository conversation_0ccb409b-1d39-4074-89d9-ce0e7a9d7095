/**
 * 测试主文章和副文章的不同消息格式
 */

const { WebhookNotificationService } = require('../lib/webhook-notification');

// 测试主文章数据
const mainArticle = {
  title: '重磅！AI技术突破性进展，改变未来十年发展格局',
  url: 'https://mp.weixin.qq.com/s/main_article_123456',
  summary: '本文深入分析了最新的AI技术突破，包括大语言模型、计算机视觉、自然语言处理等领域的重要进展。这些技术将如何影响我们的生活和工作，以及未来十年的发展趋势预测。',
  publishDate: new Date().toISOString(),
  accountName: '科技前沿观察',
  coverImage: 'https://images.unsplash.com/photo-*************-21780ecad995?w=400&h=300&fit=crop',
  isMainArticle: true
};

// 测试副文章数据
const subArticle = {
  title: '每日科技资讯汇总 | 2024年最新动态',
  url: 'https://mp.weixin.qq.com/s/sub_article_789012',
  summary: '今日科技要闻：苹果发布新产品、谷歌AI更新、特斯拉自动驾驶进展等。',
  publishDate: new Date().toISOString(),
  accountName: '科技前沿观察',
  coverImage: 'https://images.unsplash.com/photo-*************-4e9042af2176?w=400&h=300&fit=crop',
  isMainArticle: false
};

async function testArticleFormats() {
  console.log('🧪 测试主文章和副文章的不同消息格式...\n');

  // 测试企业微信webhook（需要替换为真实的webhook URL）
  const weworkWebhookUrl = process.env.WEWORK_WEBHOOK_URL || 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY';
  
  if (weworkWebhookUrl.includes('YOUR_KEY')) {
    console.log('❌ 请先配置真实的企业微信Webhook URL');
    console.log('💡 在.env文件中设置 WEWORK_WEBHOOK_URL 或直接修改脚本中的URL');
    return;
  }

  console.log('📱 测试企业微信webhook...\n');

  // 测试主文章（图文消息格式）
  console.log('🎯 测试主文章（图文消息格式）...');
  try {
    const mainResult = await WebhookNotificationService.sendNewArticleNotification(
      weworkWebhookUrl,
      'WEWORK',
      mainArticle
    );
    console.log(`主文章测试结果: ${mainResult ? '✅ 成功' : '❌ 失败'}`);
    
    if (mainResult) {
      console.log('📋 主文章消息特点:');
      console.log('  - 使用图文消息（news）格式');
      console.log('  - 包含封面图片');
      console.log('  - 显示完整摘要');
      console.log('  - 可点击跳转到原文');
    }
  } catch (error) {
    console.log(`主文章测试异常: ${error.message}`);
  }

  console.log('');

  // 等待一下，避免发送过快
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 测试副文章（文本消息格式）
  console.log('📄 测试副文章（文本消息格式）...');
  try {
    const subResult = await WebhookNotificationService.sendNewArticleNotification(
      weworkWebhookUrl,
      'WEWORK',
      subArticle
    );
    console.log(`副文章测试结果: ${subResult ? '✅ 成功' : '❌ 失败'}`);
    
    if (subResult) {
      console.log('📋 副文章消息特点:');
      console.log('  - 使用文本消息格式');
      console.log('  - 简洁的提醒样式');
      console.log('  - 包含基本信息和链接');
      console.log('  - 标注为"副文章发布提醒"');
    }
  } catch (error) {
    console.log(`副文章测试异常: ${error.message}`);
  }

  console.log('\n🏁 测试完成！');
  console.log('\n💡 消息格式说明:');
  console.log('📰 主文章: 使用图文消息（news）格式，包含封面图片，更加醒目');
  console.log('📄 副文章: 使用文本消息格式，简洁提醒，不会过于打扰用户');
  console.log('\n🔧 如需测试其他平台（钉钉、飞书），请修改脚本中的webhook类型');
}

// 测试钉钉和飞书格式的函数
async function testOtherPlatforms() {
  console.log('🧪 测试其他平台的消息格式...\n');

  // 钉钉测试
  const dingtalkWebhookUrl = process.env.DINGTALK_WEBHOOK_URL || 'https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN';
  
  if (!dingtalkWebhookUrl.includes('YOUR_TOKEN')) {
    console.log('📱 测试钉钉webhook...');
    try {
      const dingtalkResult = await WebhookNotificationService.sendNewArticleNotification(
        dingtalkWebhookUrl,
        'DINGTALK',
        mainArticle
      );
      console.log(`钉钉测试结果: ${dingtalkResult ? '✅ 成功' : '❌ 失败'}`);
    } catch (error) {
      console.log(`钉钉测试异常: ${error.message}`);
    }
  }

  // 飞书测试
  const feishuWebhookUrl = process.env.FEISHU_WEBHOOK_URL || 'https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_HOOK_ID';
  
  if (!feishuWebhookUrl.includes('YOUR_HOOK_ID')) {
    console.log('🚀 测试飞书webhook...');
    try {
      const feishuResult = await WebhookNotificationService.sendNewArticleNotification(
        feishuWebhookUrl,
        'FEISHU',
        mainArticle
      );
      console.log(`飞书测试结果: ${feishuResult ? '✅ 成功' : '❌ 失败'}`);
    } catch (error) {
      console.log(`飞书测试异常: ${error.message}`);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--all-platforms')) {
    testOtherPlatforms().catch(console.error);
  } else {
    testArticleFormats().catch(console.error);
  }
}

module.exports = { testArticleFormats, testOtherPlatforms };
