/**
 * 测试30分钟冷却期功能
 * 验证发布过新文章的公众号在30分钟内不会被定时任务选中
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function test30MinuteCooldown() {
  try {
    console.log('🧪 测试30分钟冷却期功能...\n');

    // 1. 获取所有启用爬取的公众号
    const allEnabledAccounts = await prisma.wechatAccount.findMany({
      where: {
        enableCrawling: true,
        fakeid: { not: null }
      },
      include: {
        articles: {
          orderBy: {
            publishDate: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`📊 总共有 ${allEnabledAccounts.length} 个启用定时爬取的公众号\n`);

    // 2. 模拟定时任务的新查询逻辑
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    console.log(`⏰ 30分钟前的时间: ${thirtyMinutesAgo.toLocaleString()}\n`);

    // 过滤出符合30分钟冷却期条件的公众号
    const eligibleAccounts = allEnabledAccounts.filter(account => {
      // 如果没有文章，可以执行
      if (account.articles.length === 0) {
        return true;
      }

      // 检查最新文章的发布时间
      const latestArticle = account.articles[0];
      const timeDiff = Date.now() - latestArticle.publishDate.getTime();
      const minutesAgo = Math.floor(timeDiff / (1000 * 60));

      // 如果最新文章发布时间超过30分钟，可以执行
      return minutesAgo >= 30;
    });

    console.log(`✅ 符合执行条件的公众号: ${eligibleAccounts.length} 个\n`);

    // 3. 分析每个公众号的状态
    console.log('📋 公众号状态分析:');
    console.log('='.repeat(80));

    for (const account of allEnabledAccounts) {
      const isEligible = eligibleAccounts.some(eligible => eligible.id === account.id);
      const lastArticle = account.articles[0];
      
      let status = '';
      let reason = '';
      
      if (!lastArticle) {
        status = '✅ 可执行';
        reason = '从未发布过文章';
      } else {
        const minutesAgo = Math.floor((Date.now() - lastArticle.publishDate.getTime()) / (1000 * 60));
        
        if (minutesAgo >= 30) {
          status = '✅ 可执行';
          reason = `最新文章发布于 ${minutesAgo} 分钟前`;
        } else {
          status = '⏸️ 跳过';
          reason = `最新文章发布于 ${minutesAgo} 分钟前 (冷却期内)`;
        }
      }
      
      console.log(`${status} ${account.name.padEnd(20)} | ${reason}`);
      
      // 验证逻辑是否正确
      if (isEligible && status.includes('跳过')) {
        console.log(`❌ 错误：${account.name} 应该被跳过但被选中了`);
      } else if (!isEligible && status.includes('可执行')) {
        console.log(`❌ 错误：${account.name} 应该被选中但被跳过了`);
      }
    }

    console.log('='.repeat(80));

    // 4. 统计结果
    const skippedCount = allEnabledAccounts.length - eligibleAccounts.length;
    console.log(`\n📊 统计结果:`);
    console.log(`- 总启用公众号: ${allEnabledAccounts.length}`);
    console.log(`- 可执行公众号: ${eligibleAccounts.length}`);
    console.log(`- 跳过公众号: ${skippedCount}`);
    console.log(`- 跳过比例: ${((skippedCount / allEnabledAccounts.length) * 100).toFixed(1)}%`);

    // 5. 创建测试数据（可选）
    console.log(`\n💡 提示:`);
    console.log(`- 如果想测试冷却期功能，可以手动创建一些最近发布的文章`);
    console.log(`- 或者修改现有文章的publishDate为最近30分钟内`);
    console.log(`- 然后重新运行此测试脚本查看效果`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 创建测试文章的辅助函数
async function createTestArticle(accountId, minutesAgo = 15) {
  const publishDate = new Date(Date.now() - minutesAgo * 60 * 1000);
  
  try {
    const article = await prisma.article.create({
      data: {
        title: `测试文章 - ${new Date().toLocaleString()}`,
        url: `https://mp.weixin.qq.com/s/test_${Date.now()}`,
        summary: '这是一篇用于测试30分钟冷却期功能的文章',
        publishDate: publishDate,
        wechatAccountId: accountId
      }
    });
    
    console.log(`✅ 创建测试文章成功: ${article.title}`);
    console.log(`📅 发布时间: ${publishDate.toLocaleString()} (${minutesAgo}分钟前)`);
    return article;
  } catch (error) {
    console.error('❌ 创建测试文章失败:', error);
    return null;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--create-test')) {
    // 创建测试数据模式
    console.log('🧪 创建测试数据模式...\n');
    
    prisma.wechatAccount.findFirst({
      where: { enableCrawling: true }
    }).then(account => {
      if (account) {
        return createTestArticle(account.id, 15); // 15分钟前的文章
      } else {
        console.log('❌ 未找到启用爬取的公众号');
      }
    }).then(() => {
      return test30MinuteCooldown();
    }).catch(console.error);
  } else {
    // 正常测试模式
    test30MinuteCooldown().catch(console.error);
  }
}

module.exports = { test30MinuteCooldown, createTestArticle };
