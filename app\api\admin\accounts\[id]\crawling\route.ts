import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../../lib/auth/admin';
import { prisma } from '../../../../../../lib/prisma';

// 获取公众号爬取设置
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const params = await context.params;
    const accountId = params.id;

    const account = await prisma.wechatAccount.findUnique({
      where: { id: accountId },
      select: {
        id: true,
        name: true,
        enableCrawling: true,
        lastCrawlTime: true,
        crawlStatus: true,
        crawlError: true,
        fakeid: true
      }
    });

    if (!account) {
      return NextResponse.json({ error: '公众号不存在' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      account: {
        ...account,
        canEnableCrawling: !!account.fakeid // 只有有fakeid的公众号才能启用爬取
      }
    });
  } catch (error) {
    console.error('获取公众号爬取设置失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取设置失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 更新公众号爬取设置
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.CONTENT_MODERATION)(user);

    const params = await context.params;
    const accountId = params.id;
    const { enableCrawling } = await request.json();

    // 检查公众号是否存在
    const account = await prisma.wechatAccount.findUnique({
      where: { id: accountId },
      select: { id: true, name: true, fakeid: true }
    });

    if (!account) {
      return NextResponse.json({ error: '公众号不存在' }, { status: 404 });
    }

    // 如果要启用爬取，检查是否有fakeid
    if (enableCrawling && !account.fakeid) {
      return NextResponse.json(
        { error: '该公众号缺少fakeid，无法启用定时爬取' },
        { status: 400 }
      );
    }

    // 更新设置
    const updatedAccount = await prisma.wechatAccount.update({
      where: { id: accountId },
      data: {
        enableCrawling: enableCrawling,
        // 如果禁用爬取，清除错误状态
        ...(enableCrawling === false && {
          crawlStatus: 'idle',
          crawlError: null
        })
      },
      select: {
        id: true,
        name: true,
        enableCrawling: true,
        lastCrawlTime: true,
        crawlStatus: true,
        crawlError: true,
        fakeid: true
      }
    });

    console.log(`📝 公众号 ${account.name} 爬取设置已更新: ${enableCrawling ? '启用' : '禁用'}`);

    return NextResponse.json({
      success: true,
      message: `公众号爬取设置已${enableCrawling ? '启用' : '禁用'}`,
      account: {
        ...updatedAccount,
        canEnableCrawling: !!updatedAccount.fakeid
      }
    });
  } catch (error) {
    console.error('更新公众号爬取设置失败:', error);
    const errorMessage = error instanceof Error ? error.message : '更新设置失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
