import { NextResponse } from 'next/server';

// 简单的内存缓存
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

export function withCache(handler: Function, ttl: number = 300) {
  return async (...args: any[]) => {
    const request = args[0];
    const url = new URL(request.url);
    const cacheKey = `${request.method}:${url.pathname}${url.search}`;
    
    // 检查缓存
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cached.ttl * 1000) {
      const response = NextResponse.json(cached.data);
      response.headers.set('X-Cache', 'HIT');
      response.headers.set('Cache-Control', `public, max-age=${ttl}`);
      return response;
    }
    
    // 执行原始处理器
    const response = await handler(...args);
    
    // 缓存成功响应
    if (response.status === 200) {
      try {
        const data = await response.clone().json();
        cache.set(cacheKey, {
          data,
          timestamp: Date.now(),
          ttl
        });
        
        // 清理过期缓存
        cleanExpiredCache();
      } catch (e) {
        // 忽略非JSON响应
      }
    }
    
    response.headers.set('X-Cache', 'MISS');
    response.headers.set('Cache-Control', `public, max-age=${ttl}`);
    return response;
  };
}

function cleanExpiredCache() {
  const now = Date.now();
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > value.ttl * 1000) {
      cache.delete(key);
    }
  }
}

// 清理缓存的定时器
setInterval(cleanExpiredCache, 60000); // 每分钟清理一次
