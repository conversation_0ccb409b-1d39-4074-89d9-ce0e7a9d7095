# 自动初始化功能说明

## 功能概述

FeedWe应用现在支持自动初始化功能，确保在部署后定时任务能够自动启动，无需手动干预。

## 工作原理

### 1. 自动初始化机制

- **触发时机**: 应用启动时自动执行
- **执行位置**: 服务器端（Next.js SSR环境）
- **初始化内容**: 启动微信公众号文章定时爬取任务

### 2. 核心组件

#### `lib/auto-init.ts`
- 自动初始化模块
- 确保只执行一次初始化
- 支持异步初始化和错误处理

#### `app/layout.tsx`
- 在根布局中导入自动初始化模块
- 确保应用启动时执行初始化

#### `app/api/init/route.ts`
- 提供初始化状态检查API
- 支持手动触发初始化
- 返回定时任务运行状态

## API接口

### GET /api/init
检查应用和定时任务状态

**响应示例**:
```json
{
  "success": true,
  "message": "应用运行中",
  "schedulerStatus": {
    "isRunning": true,
    "nextExecution": "2025-07-24T09:02:28.843Z",
    "consecutiveFailures": 0,
    "maxConsecutiveFailures": 5,
    "isInAllowedTime": true
  },
  "autoInitialized": true,
  "timestamp": "2025-07-24T09:00:28.844Z",
  "environment": "development"
}
```

### POST /api/init
手动触发应用初始化

**响应示例**:
```json
{
  "success": true,
  "message": "应用初始化完成",
  "schedulerStatus": {
    "isRunning": true,
    "nextExecution": "2025-07-24T09:02:28.843Z",
    "consecutiveFailures": 0,
    "maxConsecutiveFailures": 5,
    "isInAllowedTime": true
  },
  "timestamp": "2025-07-24T09:00:28.844Z"
}
```

## 部署后验证

### 1. 自动验证
部署完成后，应用会自动执行初始化，无需手动操作。

### 2. 手动验证
如果需要确认初始化状态，可以访问：
```bash
curl https://your-domain.com/api/init
```

### 3. 手动触发
如果自动初始化失败，可以手动触发：
```bash
curl -X POST https://your-domain.com/api/init
```

## 部署脚本

### `scripts/post-deploy-init.js`
部署后自动初始化脚本，支持：
- 等待应用启动
- 自动触发初始化
- 验证定时任务状态
- 详细的日志输出

**使用方法**:
```bash
npm run postdeploy
```

## 环境变量

### `DISABLE_CRAWLER_SCHEDULER`
- **默认值**: 未设置（启用定时任务）
- **设置为 `true`**: 禁用定时任务
- **用途**: 在某些环境中临时禁用定时任务

**示例**:
```env
# 禁用定时任务
DISABLE_CRAWLER_SCHEDULER=true
```

## 故障排除

### 1. 定时任务未启动
**检查步骤**:
1. 访问 `/api/init` 检查状态
2. 查看 `schedulerStatus.isRunning` 是否为 `true`
3. 检查环境变量 `DISABLE_CRAWLER_SCHEDULER`

**解决方法**:
```bash
# 手动触发初始化
curl -X POST https://your-domain.com/api/init
```

### 2. 初始化失败
**可能原因**:
- 数据库连接问题
- 环境变量配置错误
- 服务器资源不足

**解决方法**:
1. 检查应用日志
2. 验证数据库连接
3. 重新部署应用

### 3. 定时任务频繁失败
**检查项目**:
- `consecutiveFailures` 计数
- `isInAllowedTime` 时间范围
- 数据库和外部API连接

## 监控建议

### 1. 定期检查
建议定期访问 `/api/init` 检查定时任务状态

### 2. 日志监控
关注应用启动日志中的初始化信息：
- `🚀 初始化应用...`
- `🕐 启动定时爬取任务...`
- `✅ 应用初始化完成`

### 3. 告警设置
可以基于 `/api/init` 接口设置监控告警，当 `schedulerStatus.isRunning` 为 `false` 时触发告警。

## 最佳实践

1. **部署后验证**: 每次部署后检查定时任务状态
2. **环境隔离**: 在开发环境中可以禁用定时任务
3. **监控集成**: 将初始化状态集成到监控系统
4. **日志记录**: 保留详细的初始化日志用于故障排除
