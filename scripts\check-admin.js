const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAdmin() {
  try {
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (admin) {
      console.log('✅ 管理员账户存在:');
      console.log('📧 邮箱:', admin.email);
      console.log('👤 姓名:', admin.name);
      console.log('🔑 角色:', admin.role);
      console.log('✅ 状态:', admin.isActive ? '活跃' : '禁用');
      console.log('📅 创建时间:', admin.createdAt);
    } else {
      console.log('❌ 管理员账户不存在');
    }
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdmin();
