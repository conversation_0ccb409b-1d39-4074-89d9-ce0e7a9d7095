import { prisma } from './prisma';
import { WebhookNotificationService, AlertInfo } from './webhook-notification';
import { getBeijingTime, isInAllowedTimeRange, isTimeExceeded } from './beijing-time';

interface CrawlerCredentials {
  cookieString: string;
  token: string;
}

interface Article {
  title: string;
  url: string;
  publishTime: string;
  author: string;
  digest: string;
  cover?: string;
}

/**
 * 微信公众号爬取核心服务
 * 提供统一的爬取逻辑，供调度器和API端点使用
 */
export class WechatCrawlerService {
  private static instance: WechatCrawlerService;
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 5;

  private constructor() {}

  public static getInstance(): WechatCrawlerService {
    if (!WechatCrawlerService.instance) {
      WechatCrawlerService.instance = new WechatCrawlerService();
    }
    return WechatCrawlerService.instance;
  }

  /**
   * 检查当前是否在允许执行的时间范围内（北京时间6:00-23:00）
   */
  public isInAllowedTimeRange(): boolean {
    const isAllowed = isInAllowedTimeRange();
    if (!isAllowed) {
      const beijingTime = new Date();
      const hour = beijingTime.getHours();
      const minute = beijingTime.getMinutes();
      console.log(`⏰ 当前北京时间 ${hour}:${minute.toString().padStart(2, '0')}，不在允许执行时间范围内（6:00-23:00），跳过执行`);
    }
    return isAllowed;
  }

  /**
   * 获取爬虫凭据
   */
  private async getCrawlerCredentials(): Promise<CrawlerCredentials | null> {
    try {
      // 从数据库的SystemConfig表获取微信爬虫凭据
      const configNames = [
        'wechat_article_data_ticket',
        'wechat_article_rand_info',
        'wechat_article_bizuin',
        'wechat_article_slave_sid',
        'wechat_article_slave_user',
        'wechat_article_token'
      ];

      const configs = await prisma.systemConfig.findMany({
        where: {
          name: {
            in: configNames
          }
        }
      });

      if (configs.length === 0) {
        console.error('❌ 数据库中没有找到微信爬虫凭据，请先完成微信登录');
        return null;
      }

      // 构建配置映射
      const configMap: Record<string, string> = {};
      configs.forEach(config => {
        configMap[config.name] = config.value;
      });

      // 检查必要的凭据
      const token = configMap.wechat_article_token;
      if (!token) {
        console.error('❌ 缺少微信token，请重新登录');
        return null;
      }

      // 构建Cookie字符串
      const cookieFields = [
        'wechat_article_data_ticket',
        'wechat_article_rand_info',
        'wechat_article_bizuin',
        'wechat_article_slave_sid',
        'wechat_article_slave_user'
      ];

      const cookieParts: string[] = [];
      cookieFields.forEach(fieldName => {
        const value = configMap[fieldName];
        if (value) {
          // 从配置名称中提取Cookie字段名
          const cookieName = fieldName.replace('wechat_article_', '');
          cookieParts.push(`${cookieName}=${value}`);
        }
      });

      const cookieString = cookieParts.join(';');

      if (!cookieString) {
        console.error('❌ 无法构建Cookie字符串，请重新登录');
        return null;
      }

      console.log(`✅ 获取到爬虫凭据，Cookie字段数量: ${cookieParts.length}`);
      console.log(`🔑 Token: ${token.substring(0, 10)}...`);
      console.log(`🍪 Cookie长度: ${cookieString.length} 字符`);

      return {
        cookieString,
        token
      };
    } catch (error) {
      console.error('❌ 获取爬虫凭据失败:', error);
      return null;
    }
  }

  /**
   * 测试Cookie和Token的有效性
   */
  private async testCredentials(credentials: CrawlerCredentials): Promise<boolean> {
    try {
      console.log('🧪 测试Cookie和Token有效性...');

      // 使用一个简单的API来测试凭据
      const testUrl = 'https://mp.weixin.qq.com/cgi-bin/home';
      const params = new URLSearchParams({
        t: 'home/index',
        token: credentials.token,
        lang: 'zh_CN'
      });

      const response = await fetch(`${testUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Cookie': credentials.cookieString,
          'Referer': 'https://mp.weixin.qq.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        }
      });

      const isValid = response.ok && !response.url.includes('login');
      console.log(`🧪 凭据测试结果: ${isValid ? '✅ 有效' : '❌ 无效'}`);

      if (!isValid) {
        const responseText = await response.text();
        if (responseText.includes('login') || responseText.includes('登录')) {
          console.error('❌ Cookie已过期，需要重新登录');
        }
      }

      return isValid;
    } catch (error) {
      console.error('❌ 测试凭据时出错:', error);
      return false;
    }
  }

  /**
   * 爬取单个公众号的文章
   */
  private async crawlAccountArticles(account: any, credentials: CrawlerCredentials): Promise<Article[]> {
    try {
      console.log(`📡 调用微信公众平台API获取文章: ${account.name}`);

      const apiUrl = 'https://mp.weixin.qq.com/cgi-bin/appmsgpublish';
      const params = new URLSearchParams({
        sub_action: 'list_ex',
        sub: 'list',
        begin: '0',
        count: '1',
        type: '101_1',
        lang: 'lang',
        f: "json",
        ajax: '1',
        id: account.fakeid,
        token: credentials.token
      });

      const response = await fetch(`${apiUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Cookie': credentials.cookieString,
          'Referer': 'https://mp.weixin.qq.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      });

      console.log(`📊 API响应状态: ${response.status} ${response.statusText}`);
      console.log(`📊 响应头 Content-Type: ${response.headers.get('content-type')}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API请求失败，响应内容: ${errorText.substring(0, 500)}...`);
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error(`❌ API返回非JSON响应，Content-Type: ${contentType}`);
        console.error(`❌ 响应内容前500字符: ${responseText.substring(0, 500)}...`);

        // 检查是否是登录页面
        if (responseText.includes('<!DOCTYPE') && responseText.includes('login')) {
          throw new Error('Cookie已过期，需要重新登录微信公众平台');
        }

        throw new Error(`API返回非JSON响应，可能是Cookie过期或请求参数错误`);
      }

      const result = await response.json();

      console.log('📊 API响应基本信息:', {
        hasBaseResp: !!result.base_resp,
        baseRespRet: result.base_resp?.ret,
        responseKeys: Object.keys(result)
      });

      if (result.base_resp && result.base_resp.ret !== 0) {
        console.error('❌ API返回错误:', result.base_resp);
        throw new Error(`API返回错误: ${result.base_resp.err_msg || '未知错误'}`);
      }

      const articles: Article[] = [];

      console.log('📊 API响应结构:', {
        hasPublishPage: !!result.publish_page,
        publishPageType: typeof result.publish_page,
        publishPageLength: result.publish_page ? result.publish_page.length : 0
      });

      // 解析文章列表 - publish_page是JSON字符串，需要先解析
      if (result.publish_page) {
        let publishPageData;

        try {
          // 如果publish_page是字符串，需要解析为JSON
          if (typeof result.publish_page === 'string') {
            console.log('📝 publish_page是字符串，正在解析JSON...');
            publishPageData = JSON.parse(result.publish_page);
          } else {
            // 如果已经是对象，直接使用
            publishPageData = result.publish_page;
          }

          console.log('📊 解析后的publish_page结构:', {
            hasPublishList: !!publishPageData.publish_list,
            publishListLength: publishPageData.publish_list ? publishPageData.publish_list.length : 0
          });

          if (publishPageData.publish_list && Array.isArray(publishPageData.publish_list)) {
            const publishList = publishPageData.publish_list;

            for (const publishItem of publishList) {
              if (publishItem.publish_info && publishItem.publish_info.appmsgex) {
                const articleList = publishItem.publish_info.appmsgex;

                for (const articleItem of articleList) {
                  // 将时间戳转换为北京时间
                  const publishTimestamp = publishItem.publish_info.sent_time * 1000;
                  const publishDate = new Date(publishTimestamp);

                  const article: Article = {
                    title: articleItem.title || '无标题',
                    url: articleItem.link || '',
                    publishTime: publishDate.toISOString(),
                    author: articleItem.author || account.name,
                    digest: articleItem.digest || '',
                    cover: articleItem.cover || ''
                  };

                  articles.push(article);
                  console.log(`📄 解析文章: ${article.title}`);
                }
              }
            }
          } else {
            console.log('⚠️ publish_list不存在或不是数组');
          }
        } catch (parseError) {
          console.error('❌ 解析publish_page JSON失败:', parseError);
          console.error('❌ publish_page内容:', result.publish_page.substring(0, 500) + '...');
          throw new Error(`解析文章列表失败: ${parseError instanceof Error ? parseError.message : '未知错误'}`);
        }
      } else {
        console.log('⚠️ API响应中没有publish_page字段');
      }

      console.log(`✅ 成功获取 ${articles.length} 篇文章: ${account.name}`);
      return articles;
    } catch (error) {
      console.error(`❌ 爬取公众号文章失败 (${account.name}):`, error);
      throw error;
    }
  }

  /**
   * 保存文章到数据库
   */
  private async saveArticlesToDatabase(articles: Article[], accountId: string): Promise<number> {
    let newArticlesCount = 0;

    for (const article of articles) {
      try {
        // 检查文章是否已存在
        const existingArticle = await prisma.article.findFirst({
          where: {
            title: article.title,
            wechatAccountId: accountId
          }
        });

        if (!existingArticle) {
          // 创建新文章
          await prisma.article.create({
            data: {
              title: article.title,
              url: article.url,
              publishDate: new Date(article.publishTime),
              author: article.author,
              summary: article.digest,
              coverImage: article.cover,
              wechatAccountId: accountId
            }
          });
          newArticlesCount++;
          console.log(`📝 新增文章: ${article.title}`);
        }
      } catch (error) {
        console.error(`❌ 保存文章失败 (${article.title}):`, error);
      }
    }

    return newArticlesCount;
  }

  /**
   * 获取需要爬取的公众号列表
   */
  private async getAccountsToCrawl(): Promise<any[]> {
    
    // 获取所有启用爬取的公众号
    const allEnabledAccounts = await prisma.wechatAccount.findMany({
      where: {
        enableCrawling: true,
        fakeid: { not: null }
      },
      include: {
        articles: {
          orderBy: {
            publishDate: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`📊 找到 ${allEnabledAccounts.length} 个启用爬取的公众号`);

    // 筛选需要爬取的账号（距离上次爬取超过30分钟）
    const accountsToCrawl = allEnabledAccounts.filter(account => {
      if (!account.lastCrawlTime) {
        console.log(`🆕 ${account.name} 从未爬取过，将进行首次爬取`);
        return true; // 从未爬取过
      }

      // 使用北京时间工具函数检查时间间隔
      const shouldCrawl = isTimeExceeded(account.lastCrawlTime, 30);

      if (!shouldCrawl) {
        const timeSinceLastCrawl = getBeijingTime().getTime() - account.lastCrawlTime.getTime();
        const remainingMinutes = Math.ceil((30 * 60 * 1000 - timeSinceLastCrawl) / (60 * 1000));
        console.log(`⏳ ${account.name} 距离上次爬取不足30分钟，还需等待 ${remainingMinutes} 分钟`);
      } else {
        console.log(`✅ ${account.name} 距离上次爬取已超过30分钟，可以进行爬取`);
      }

      return shouldCrawl;
    });

    console.log(`🎯 筛选出 ${accountsToCrawl.length} 个需要爬取的公众号`);
    return accountsToCrawl;
  }

  /**
   * 执行单次爬取任务
   */
  public async executeCrawlTask(): Promise<{ success: boolean; message: string; stats?: any }> {
    console.log('🚀 开始执行文章爬取任务');
    
    try {
      // 检查时间范围
      if (!this.isInAllowedTimeRange()) {
        return {
          success: true,
          message: '不在执行时间范围内'
        };
      }

      // 获取需要爬取的公众号
      const accountsToCrawl = await this.getAccountsToCrawl();
      
      if (accountsToCrawl.length === 0) {
        console.log('📝 没有需要爬取的公众号');
        return {
          success: true,
          message: '没有需要爬取的公众号'
        };
      }

      let totalNewArticles = 0;
      let successfulAccounts = 0;
      let failedAccounts = 0;

      // 获取爬虫凭据
      const credentials = await this.getCrawlerCredentials();
      if (!credentials) {
        return {
          success: false,
          message: '无法获取爬虫凭据'
        };
      }

      // 测试凭据有效性
      const isCredentialsValid = await this.testCredentials(credentials);
      if (!isCredentialsValid) {
        return {
          success: false,
          message: 'Cookie或Token已过期，请重新登录微信公众平台'
        };
      }

      // 逐个处理公众号
      for (const account of accountsToCrawl) {
        try {
          console.log(`\n🔍 开始爬取公众号: ${account.name}`);

          // 执行真实的爬取逻辑
          const articles = await this.crawlAccountArticles(account, credentials);

          // 保存文章到数据库
          const newArticlesCount = await this.saveArticlesToDatabase(articles, account.id);
          totalNewArticles += newArticlesCount;

          // 只有在爬取到新文章时才更新lastCrawlTime
          if (newArticlesCount > 0) {
            await prisma.wechatAccount.update({
              where: { id: account.id },
              data: {
                lastCrawlTime: getBeijingTime(), // 使用北京时间
                crawlStatus: 'idle',
                crawlError: null
              }
            });
            console.log(`✅ ${account.name} 爬取完成，新增 ${newArticlesCount} 篇文章，已更新lastCrawlTime`);
          } else {
            // 没有新文章时，只更新状态，不更新lastCrawlTime
            await prisma.wechatAccount.update({
              where: { id: account.id },
              data: {
                crawlStatus: 'idle',
                crawlError: null
              }
            });
            console.log(`✅ ${account.name} 爬取完成，没有新文章，lastCrawlTime保持不变`);
          }

          successfulAccounts++;

          // 添加延迟避免请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 3000));

        } catch (error) {
          failedAccounts++;
          console.error(`❌ 爬取公众号 ${account.name} 时出错:`, error);

          // 更新错误状态
          try {
            await prisma.wechatAccount.update({
              where: { id: account.id },
              data: {
                crawlStatus: 'error',
                crawlError: error instanceof Error ? error.message : '未知错误'
              }
            });
          } catch (updateError) {
            console.error(`❌ 更新公众号错误状态失败:`, updateError);
          }
        }
      }

      // 重置连续失败计数
      if (successfulAccounts > 0) {
        this.consecutiveFailures = 0;
      } else {
        this.consecutiveFailures++;
      }

      const stats = {
        totalAccounts: accountsToCrawl.length,
        successfulAccounts,
        failedAccounts,
        totalNewArticles
      };

      console.log(`\n📊 爬取任务完成统计:`);
      console.log(`  - 处理公众号: ${stats.totalAccounts} 个`);
      console.log(`  - 成功: ${stats.successfulAccounts} 个`);
      console.log(`  - 失败: ${stats.failedAccounts} 个`);
      console.log(`  - 新增文章: ${stats.totalNewArticles} 篇`);

      // 检查是否需要发送告警
      await this.checkAndSendAlert();

      return {
        success: true,
        message: '爬取任务执行完成',
        stats
      };

    } catch (error) {
      this.consecutiveFailures++;
      console.error('❌ 爬取任务执行失败:', error);
      
      // 检查是否需要发送告警
      await this.checkAndSendAlert();

      return {
        success: false,
        message: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 检查并发送告警通知
   */
  private async checkAndSendAlert(): Promise<void> {
    if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
      console.log(`🚨 连续失败 ${this.consecutiveFailures} 次，发送告警通知`);
      
      try {
        // 获取管理员用户的webhook配置
        const adminUsers = await prisma.user.findMany({
          where: {
            role: 'ADMIN',
            webhookUrl: { not: null },
            webhookType: { not: null }
          }
        });

        const webhookService = new WebhookNotificationService();

        for (const admin of adminUsers) {
          if (admin.webhookUrl && admin.webhookType) {
            try {
              const webhook = {
                url: admin.webhookUrl,
                type: admin.webhookType,
                isActive: true
              };

              await webhookService.sendAlert(webhook, {
                title: '🚨 微信公众号爬取服务告警',
                message: `爬取服务已连续失败 ${this.consecutiveFailures} 次，请检查服务状态。`,
                timestamp: new Date().toISOString(),
                level: 'ERROR'
              });
              console.log(`✅ 告警通知已发送到管理员 ${admin.email}`);
            } catch (error) {
              console.error(`❌ 发送告警通知失败 (${admin.email}):`, error);
            }
          }
        }
      } catch (error) {
        console.error('❌ 获取管理员信息失败:', error);
      }
    }
  }

  /**
   * 获取连续失败次数
   */
  public getConsecutiveFailures(): number {
    return this.consecutiveFailures;
  }

  /**
   * 重置连续失败计数
   */
  public resetConsecutiveFailures(): void {
    this.consecutiveFailures = 0;
  }
}
