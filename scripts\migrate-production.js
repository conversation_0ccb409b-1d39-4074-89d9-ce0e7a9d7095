#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🗄️ 运行生产环境数据库迁移...');

try {
  // 使用直连URL运行迁移
  const directUrl = process.env.DIRECT_URL || process.env.DATABASE_URL;
  
  if (!directUrl) {
    throw new Error('未找到数据库连接字符串');
  }

  console.log('📦 生成Prisma客户端...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  console.log('🔄 运行数据库迁移...');
  execSync('npx prisma migrate deploy', {
    stdio: 'inherit',
    env: {
      ...process.env,
      DATABASE_URL: directUrl
    }
  });

  console.log('✅ 迁移完成！');
} catch (error) {
  console.error('❌ 迁移失败:', error.message);
  process.exit(1);
}
