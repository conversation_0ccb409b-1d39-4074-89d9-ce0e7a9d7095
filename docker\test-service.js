#!/usr/bin/env node

/**
 * 本地测试 crawler-service.js 的脚本
 * 用于验证模块导入和基本功能
 */

const path = require('path');
const fs = require('fs');

console.log('🧪 测试 crawler-service.js...');

// 检查文件是否存在
const serviceFile = path.join(__dirname, 'crawler-service.js');
if (!fs.existsSync(serviceFile)) {
  console.error('❌ crawler-service.js 文件不存在');
  process.exit(1);
}

// 检查必要的目录
const libDir = path.join(__dirname, '..', 'lib');
if (!fs.existsSync(libDir)) {
  console.error('❌ lib 目录不存在');
  process.exit(1);
}

// 检查关键文件
const requiredFiles = [
  'lib/wechat-crawler-scheduler.ts',
  'lib/startup.ts',
  'prisma/schema.prisma'
];

for (const file of requiredFiles) {
  const filePath = path.join(__dirname, '..', file);
  if (!fs.existsSync(filePath)) {
    console.error(`❌ 必需文件不存在: ${file}`);
    process.exit(1);
  }
}

console.log('✅ 所有必需文件都存在');

// 模拟 Docker 环境中的路径结构
console.log('📁 模拟 Docker 环境路径结构:');
console.log('  /app/crawler-service.js');
console.log('  /app/lib/wechat-crawler-scheduler.ts');
console.log('  /app/lib/startup.ts');
console.log('  /app/prisma/schema.prisma');

// 测试模块导入路径
console.log('🔍 测试模块导入路径...');

// 在 Docker 环境中，文件结构应该是：
// /app/crawler-service.js
// /app/lib/wechat-crawler-scheduler.js
// /app/lib/startup.js

const dockerPaths = {
  'crawler-service.js': '/app/crawler-service.js',
  'lib/wechat-crawler-scheduler.js': '/app/lib/wechat-crawler-scheduler.js',
  'lib/startup.js': '/app/lib/startup.js'
};

console.log('📋 Docker 环境中的导入路径应该是:');
console.log('  import("./lib/wechat-crawler-scheduler.js")');
console.log('  import("./lib/startup.js")');

// 检查 TypeScript 文件是否需要编译
const tsFiles = fs.readdirSync(libDir).filter(file => file.endsWith('.ts'));
if (tsFiles.length > 0) {
  console.log('⚠️ 发现 TypeScript 文件，可能需要编译:');
  tsFiles.forEach(file => console.log(`    lib/${file}`));
  console.log('💡 建议: 在 Dockerfile 中添加 TypeScript 编译步骤');
}

console.log('✅ 路径检查完成');

// 提供修复建议
console.log('\n🔧 修复建议:');
console.log('1. 确保 Dockerfile 正确复制了所有必需文件');
console.log('2. 检查 crawler-service.js 中的导入路径');
console.log('3. 如果使用 TypeScript，需要先编译为 JavaScript');
console.log('4. 验证 Docker 容器中的文件结构');

console.log('\n🚀 下一步:');
console.log('1. 修复 Dockerfile 和导入路径');
console.log('2. 重新构建 Docker 镜像');
console.log('3. 测试容器启动');

process.exit(0);
