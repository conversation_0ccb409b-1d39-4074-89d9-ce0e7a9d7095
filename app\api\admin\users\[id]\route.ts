import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../../lib/auth/jwt';
import { requireAdmin, Permission } from '../../../../../lib/auth/admin';
import { prisma } from '../../../../../lib/prisma';

// 更新用户状态
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.USER_MANAGEMENT)(user);

    const { isActive } = await request.json();
    const params = await context.params;
    const userId = params.id;

    // 更新用户状态
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { isActive },
      select: {
        id: true,
        email: true,
        name: true,
        isActive: true
      }
    });

    return NextResponse.json({
      success: true,
      user: updatedUser
    });
  } catch (error) {
    console.error('更新用户失败:', error);
    const errorMessage = error instanceof Error ? error.message : '更新用户失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}

// 删除用户
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // 验证管理员权限
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const user = verifyToken(token);
    
    // 检查管理员权限
    requireAdmin(Permission.USER_MANAGEMENT)(user);

    const params = await context.params;
    const userId = params.id;

    // 检查是否为管理员账户
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, email: true }
    });

    if (targetUser?.role !== 'USER') {
      return NextResponse.json(
        { error: '不能删除管理员账户' },
        { status: 403 }
      );
    }

    // 删除用户相关数据
    await prisma.$transaction(async (tx) => {
      // 删除用户的订阅
      await tx.subscription.deleteMany({
        where: { userId }
      });

      // 删除用户的邀请奖励
      await tx.inviteReward.deleteMany({
        where: { inviterId: userId }
      });

      // 删除用户的订单
      await tx.order.deleteMany({
        where: { userId }
      });

      // 删除用户
      await tx.user.delete({
        where: { id: userId }
      });
    });

    return NextResponse.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('删除用户失败:', error);
    const errorMessage = error instanceof Error ? error.message : '删除用户失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: errorMessage.includes('权限') ? 403 : 500 }
    );
  }
}
