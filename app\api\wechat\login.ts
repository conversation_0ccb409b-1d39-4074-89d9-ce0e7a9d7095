// import { NextApiRequest, NextApiResponse } from 'next';
// import { WeChatLogin } from '../../../lib/wechat-login';

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {
//   if (req.method !== 'POST') {
//     return res.status(405).json({ error: 'Method not allowed' });
//   }

//   const wechatLogin = new WeChatLogin();

//   try {
//     const session = await wechatLogin.login((status, data) => {
//       // 这里可以通过 WebSocket 或 SSE 实时推送状态
//       console.log('Login status:', status, data);
//     });

//     res.status(200).json({
//       success: true,
//       sessionId: session.sessionId,
//       token: session.token,
//       cookies: session.cookies,
//       userInfo: session.userInfo
//     });
//   } catch (error) {
//     res.status(400).json({
//       success: false,
//       error: error.message
//     });
//   }
// }